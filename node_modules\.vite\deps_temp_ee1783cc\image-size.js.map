{"version": 3, "sources": ["../../image-size/dist/index.mjs"], "sourcesContent": ["// lib/types/utils.ts\nvar decoder = new TextDecoder();\nvar toUTF8String = (input, start = 0, end = input.length) => decoder.decode(input.slice(start, end));\nvar toHexString = (input, start = 0, end = input.length) => input.slice(start, end).reduce((memo, i) => memo + `0${i.toString(16)}`.slice(-2), \"\");\nvar getView = (input, offset) => new DataView(input.buffer, input.byteOffset + offset);\nvar readInt16LE = (input, offset = 0) => getView(input, offset).getInt16(0, true);\nvar readUInt16BE = (input, offset = 0) => getView(input, offset).getUint16(0, false);\nvar readUInt16LE = (input, offset = 0) => getView(input, offset).getUint16(0, true);\nvar readUInt24LE = (input, offset = 0) => {\n  const view = getView(input, offset);\n  return view.getUint16(0, true) + (view.getUint8(2) << 16);\n};\nvar readInt32LE = (input, offset = 0) => getView(input, offset).getInt32(0, true);\nvar readUInt32BE = (input, offset = 0) => getView(input, offset).getUint32(0, false);\nvar readUInt32LE = (input, offset = 0) => getView(input, offset).getUint32(0, true);\nvar readUInt64 = (input, offset, isBigEndian) => getView(input, offset).getBigUint64(0, !isBigEndian);\nvar methods = {\n  readUInt16BE,\n  readUInt16LE,\n  readUInt32BE,\n  readUInt32LE\n};\nfunction readUInt(input, bits, offset = 0, isBigEndian = false) {\n  const endian = isBigEndian ? \"BE\" : \"LE\";\n  const methodName = `readUInt${bits}${endian}`;\n  return methods[methodName](input, offset);\n}\nfunction readBox(input, offset) {\n  if (input.length - offset < 4) return;\n  const boxSize = readUInt32BE(input, offset);\n  if (input.length - offset < boxSize) return;\n  return {\n    name: toUTF8String(input, 4 + offset, 8 + offset),\n    offset,\n    size: boxSize\n  };\n}\nfunction findBox(input, boxName, currentOffset) {\n  while (currentOffset < input.length) {\n    const box = readBox(input, currentOffset);\n    if (!box) break;\n    if (box.name === boxName) return box;\n    currentOffset += box.size > 0 ? box.size : 8;\n  }\n}\n\n// lib/types/bmp.ts\nvar BMP = {\n  validate: (input) => toUTF8String(input, 0, 2) === \"BM\",\n  calculate: (input) => ({\n    height: Math.abs(readInt32LE(input, 22)),\n    width: readUInt32LE(input, 18)\n  })\n};\n\n// lib/types/ico.ts\nvar TYPE_ICON = 1;\nvar SIZE_HEADER = 2 + 2 + 2;\nvar SIZE_IMAGE_ENTRY = 1 + 1 + 1 + 1 + 2 + 2 + 4 + 4;\nfunction getSizeFromOffset(input, offset) {\n  const value = input[offset];\n  return value === 0 ? 256 : value;\n}\nfunction getImageSize(input, imageIndex) {\n  const offset = SIZE_HEADER + imageIndex * SIZE_IMAGE_ENTRY;\n  return {\n    height: getSizeFromOffset(input, offset + 1),\n    width: getSizeFromOffset(input, offset)\n  };\n}\nvar ICO = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_ICON;\n  },\n  calculate(input) {\n    const nbImages = readUInt16LE(input, 4);\n    const imageSize2 = getImageSize(input, 0);\n    if (nbImages === 1) return imageSize2;\n    const images = [];\n    for (let imageIndex = 0; imageIndex < nbImages; imageIndex += 1) {\n      images.push(getImageSize(input, imageIndex));\n    }\n    return {\n      width: imageSize2.width,\n      height: imageSize2.height,\n      images\n    };\n  }\n};\n\n// lib/types/cur.ts\nvar TYPE_CURSOR = 2;\nvar CUR = {\n  validate(input) {\n    const reserved = readUInt16LE(input, 0);\n    const imageCount = readUInt16LE(input, 4);\n    if (reserved !== 0 || imageCount === 0) return false;\n    const imageType = readUInt16LE(input, 2);\n    return imageType === TYPE_CURSOR;\n  },\n  calculate: (input) => ICO.calculate(input)\n};\n\n// lib/types/dds.ts\nvar DDS = {\n  validate: (input) => readUInt32LE(input, 0) === 542327876,\n  calculate: (input) => ({\n    height: readUInt32LE(input, 12),\n    width: readUInt32LE(input, 16)\n  })\n};\n\n// lib/types/gif.ts\nvar gifRegexp = /^GIF8[79]a/;\nvar GIF = {\n  validate: (input) => gifRegexp.test(toUTF8String(input, 0, 6)),\n  calculate: (input) => ({\n    height: readUInt16LE(input, 8),\n    width: readUInt16LE(input, 6)\n  })\n};\n\n// lib/types/heif.ts\nvar brandMap = {\n  avif: \"avif\",\n  mif1: \"heif\",\n  msf1: \"heif\",\n  // heif-sequence\n  heic: \"heic\",\n  heix: \"heic\",\n  hevc: \"heic\",\n  // heic-sequence\n  hevx: \"heic\"\n  // heic-sequence\n};\nvar HEIF = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"ftyp\") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand in brandMap;\n  },\n  calculate(input) {\n    const metaBox = findBox(input, \"meta\", 0);\n    const iprpBox = metaBox && findBox(input, \"iprp\", metaBox.offset + 12);\n    const ipcoBox = iprpBox && findBox(input, \"ipco\", iprpBox.offset + 8);\n    if (!ipcoBox) {\n      throw new TypeError(\"Invalid HEIF, no ipco box found\");\n    }\n    const type = toUTF8String(input, 8, 12);\n    const images = [];\n    let currentOffset = ipcoBox.offset + 8;\n    while (currentOffset < ipcoBox.offset + ipcoBox.size) {\n      const ispeBox = findBox(input, \"ispe\", currentOffset);\n      if (!ispeBox) break;\n      const rawWidth = readUInt32BE(input, ispeBox.offset + 12);\n      const rawHeight = readUInt32BE(input, ispeBox.offset + 16);\n      const clapBox = findBox(input, \"clap\", currentOffset);\n      let width = rawWidth;\n      let height = rawHeight;\n      if (clapBox && clapBox.offset < ipcoBox.offset + ipcoBox.size) {\n        const cropRight = readUInt32BE(input, clapBox.offset + 12);\n        width = rawWidth - cropRight;\n      }\n      images.push({ height, width });\n      currentOffset = ispeBox.offset + ispeBox.size;\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid HEIF, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      type,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/icns.ts\nvar SIZE_HEADER2 = 4 + 4;\nvar FILE_LENGTH_OFFSET = 4;\nvar ENTRY_LENGTH_OFFSET = 4;\nvar ICON_TYPE_SIZE = {\n  ICON: 32,\n  \"ICN#\": 32,\n  // m => 16 x 16\n  \"icm#\": 16,\n  icm4: 16,\n  icm8: 16,\n  // s => 16 x 16\n  \"ics#\": 16,\n  ics4: 16,\n  ics8: 16,\n  is32: 16,\n  s8mk: 16,\n  icp4: 16,\n  // l => 32 x 32\n  icl4: 32,\n  icl8: 32,\n  il32: 32,\n  l8mk: 32,\n  icp5: 32,\n  ic11: 32,\n  // h => 48 x 48\n  ich4: 48,\n  ich8: 48,\n  ih32: 48,\n  h8mk: 48,\n  // . => 64 x 64\n  icp6: 64,\n  ic12: 32,\n  // t => 128 x 128\n  it32: 128,\n  t8mk: 128,\n  ic07: 128,\n  // . => 256 x 256\n  ic08: 256,\n  ic13: 256,\n  // . => 512 x 512\n  ic09: 512,\n  ic14: 512,\n  // . => 1024 x 1024\n  ic10: 1024\n};\nfunction readImageHeader(input, imageOffset) {\n  const imageLengthOffset = imageOffset + ENTRY_LENGTH_OFFSET;\n  return [\n    toUTF8String(input, imageOffset, imageLengthOffset),\n    readUInt32BE(input, imageLengthOffset)\n  ];\n}\nfunction getImageSize2(type) {\n  const size = ICON_TYPE_SIZE[type];\n  return { width: size, height: size, type };\n}\nvar ICNS = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"icns\",\n  calculate(input) {\n    const inputLength = input.length;\n    const fileLength = readUInt32BE(input, FILE_LENGTH_OFFSET);\n    let imageOffset = SIZE_HEADER2;\n    const images = [];\n    while (imageOffset < fileLength && imageOffset < inputLength) {\n      const imageHeader = readImageHeader(input, imageOffset);\n      const imageSize2 = getImageSize2(imageHeader[0]);\n      images.push(imageSize2);\n      imageOffset += imageHeader[1];\n    }\n    if (images.length === 0) {\n      throw new TypeError(\"Invalid ICNS, no sizes found\");\n    }\n    return {\n      width: images[0].width,\n      height: images[0].height,\n      ...images.length > 1 ? { images } : {}\n    };\n  }\n};\n\n// lib/types/j2c.ts\nvar J2C = {\n  // TODO: this doesn't seem right. SIZ marker doesn't have to be right after the SOC\n  validate: (input) => readUInt32BE(input, 0) === 4283432785,\n  calculate: (input) => ({\n    height: readUInt32BE(input, 12),\n    width: readUInt32BE(input, 8)\n  })\n};\n\n// lib/types/jp2.ts\nvar JP2 = {\n  validate(input) {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"jP  \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jp2 \";\n  },\n  calculate(input) {\n    const jp2hBox = findBox(input, \"jp2h\", 0);\n    const ihdrBox = jp2hBox && findBox(input, \"ihdr\", jp2hBox.offset + 8);\n    if (ihdrBox) {\n      return {\n        height: readUInt32BE(input, ihdrBox.offset + 8),\n        width: readUInt32BE(input, ihdrBox.offset + 12)\n      };\n    }\n    throw new TypeError(\"Unsupported JPEG 2000 format\");\n  }\n};\n\n// lib/types/jpg.ts\nvar EXIF_MARKER = \"45786966\";\nvar APP1_DATA_SIZE_BYTES = 2;\nvar EXIF_HEADER_BYTES = 6;\nvar TIFF_BYTE_ALIGN_BYTES = 2;\nvar BIG_ENDIAN_BYTE_ALIGN = \"4d4d\";\nvar LITTLE_ENDIAN_BYTE_ALIGN = \"4949\";\nvar IDF_ENTRY_BYTES = 12;\nvar NUM_DIRECTORY_ENTRIES_BYTES = 2;\nfunction isEXIF(input) {\n  return toHexString(input, 2, 6) === EXIF_MARKER;\n}\nfunction extractSize(input, index) {\n  return {\n    height: readUInt16BE(input, index),\n    width: readUInt16BE(input, index + 2)\n  };\n}\nfunction extractOrientation(exifBlock, isBigEndian) {\n  const idfOffset = 8;\n  const offset = EXIF_HEADER_BYTES + idfOffset;\n  const idfDirectoryEntries = readUInt(exifBlock, 16, offset, isBigEndian);\n  for (let directoryEntryNumber = 0; directoryEntryNumber < idfDirectoryEntries; directoryEntryNumber++) {\n    const start = offset + NUM_DIRECTORY_ENTRIES_BYTES + directoryEntryNumber * IDF_ENTRY_BYTES;\n    const end = start + IDF_ENTRY_BYTES;\n    if (start > exifBlock.length) {\n      return;\n    }\n    const block = exifBlock.slice(start, end);\n    const tagNumber = readUInt(block, 16, 0, isBigEndian);\n    if (tagNumber === 274) {\n      const dataFormat = readUInt(block, 16, 2, isBigEndian);\n      if (dataFormat !== 3) {\n        return;\n      }\n      const numberOfComponents = readUInt(block, 32, 4, isBigEndian);\n      if (numberOfComponents !== 1) {\n        return;\n      }\n      return readUInt(block, 16, 8, isBigEndian);\n    }\n  }\n}\nfunction validateExifBlock(input, index) {\n  const exifBlock = input.slice(APP1_DATA_SIZE_BYTES, index);\n  const byteAlign = toHexString(\n    exifBlock,\n    EXIF_HEADER_BYTES,\n    EXIF_HEADER_BYTES + TIFF_BYTE_ALIGN_BYTES\n  );\n  const isBigEndian = byteAlign === BIG_ENDIAN_BYTE_ALIGN;\n  const isLittleEndian = byteAlign === LITTLE_ENDIAN_BYTE_ALIGN;\n  if (isBigEndian || isLittleEndian) {\n    return extractOrientation(exifBlock, isBigEndian);\n  }\n}\nfunction validateInput(input, index) {\n  if (index > input.length) {\n    throw new TypeError(\"Corrupt JPG, exceeded buffer limits\");\n  }\n}\nvar JPG = {\n  validate: (input) => toHexString(input, 0, 2) === \"ffd8\",\n  calculate(_input) {\n    let input = _input.slice(4);\n    let orientation;\n    let next;\n    while (input.length) {\n      const i = readUInt16BE(input, 0);\n      validateInput(input, i);\n      if (input[i] !== 255) {\n        input = input.slice(1);\n        continue;\n      }\n      if (isEXIF(input)) {\n        orientation = validateExifBlock(input, i);\n      }\n      next = input[i + 1];\n      if (next === 192 || next === 193 || next === 194) {\n        const size = extractSize(input, i + 5);\n        if (!orientation) {\n          return size;\n        }\n        return {\n          height: size.height,\n          orientation,\n          width: size.width\n        };\n      }\n      input = input.slice(i + 2);\n    }\n    throw new TypeError(\"Invalid JPG, no size found\");\n  }\n};\n\n// lib/utils/bit-reader.ts\nvar BitReader = class {\n  constructor(input, endianness) {\n    this.input = input;\n    this.endianness = endianness;\n    // Skip the first 16 bits (2 bytes) of signature\n    this.byteOffset = 2;\n    this.bitOffset = 0;\n  }\n  /** Reads a specified number of bits, and move the offset */\n  getBits(length = 1) {\n    let result = 0;\n    let bitsRead = 0;\n    while (bitsRead < length) {\n      if (this.byteOffset >= this.input.length) {\n        throw new Error(\"Reached end of input\");\n      }\n      const currentByte = this.input[this.byteOffset];\n      const bitsLeft = 8 - this.bitOffset;\n      const bitsToRead = Math.min(length - bitsRead, bitsLeft);\n      if (this.endianness === \"little-endian\") {\n        const mask = (1 << bitsToRead) - 1;\n        const bits = currentByte >> this.bitOffset & mask;\n        result |= bits << bitsRead;\n      } else {\n        const mask = (1 << bitsToRead) - 1 << 8 - this.bitOffset - bitsToRead;\n        const bits = (currentByte & mask) >> 8 - this.bitOffset - bitsToRead;\n        result = result << bitsToRead | bits;\n      }\n      bitsRead += bitsToRead;\n      this.bitOffset += bitsToRead;\n      if (this.bitOffset === 8) {\n        this.byteOffset++;\n        this.bitOffset = 0;\n      }\n    }\n    return result;\n  }\n};\n\n// lib/types/jxl-stream.ts\nfunction calculateImageDimension(reader, isSmallImage) {\n  if (isSmallImage) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  const sizeClass = reader.getBits(2);\n  const extraBits = [9, 13, 18, 30][sizeClass];\n  return 1 + reader.getBits(extraBits);\n}\nfunction calculateImageWidth(reader, isSmallImage, widthMode, height) {\n  if (isSmallImage && widthMode === 0) {\n    return 8 * (1 + reader.getBits(5));\n  }\n  if (widthMode === 0) {\n    return calculateImageDimension(reader, false);\n  }\n  const aspectRatios = [1, 1.2, 4 / 3, 1.5, 16 / 9, 5 / 4, 2];\n  return Math.floor(height * aspectRatios[widthMode - 1]);\n}\nvar JXLStream = {\n  validate: (input) => {\n    return toHexString(input, 0, 2) === \"ff0a\";\n  },\n  calculate(input) {\n    const reader = new BitReader(input, \"little-endian\");\n    const isSmallImage = reader.getBits(1) === 1;\n    const height = calculateImageDimension(reader, isSmallImage);\n    const widthMode = reader.getBits(3);\n    const width = calculateImageWidth(reader, isSmallImage, widthMode, height);\n    return { width, height };\n  }\n};\n\n// lib/types/jxl.ts\nfunction extractCodestream(input) {\n  const jxlcBox = findBox(input, \"jxlc\", 0);\n  if (jxlcBox) {\n    return input.slice(jxlcBox.offset + 8, jxlcBox.offset + jxlcBox.size);\n  }\n  const partialStreams = extractPartialStreams(input);\n  if (partialStreams.length > 0) {\n    return concatenateCodestreams(partialStreams);\n  }\n  return void 0;\n}\nfunction extractPartialStreams(input) {\n  const partialStreams = [];\n  let offset = 0;\n  while (offset < input.length) {\n    const jxlpBox = findBox(input, \"jxlp\", offset);\n    if (!jxlpBox) break;\n    partialStreams.push(\n      input.slice(jxlpBox.offset + 12, jxlpBox.offset + jxlpBox.size)\n    );\n    offset = jxlpBox.offset + jxlpBox.size;\n  }\n  return partialStreams;\n}\nfunction concatenateCodestreams(partialCodestreams) {\n  const totalLength = partialCodestreams.reduce(\n    (acc, curr) => acc + curr.length,\n    0\n  );\n  const codestream = new Uint8Array(totalLength);\n  let position = 0;\n  for (const partial of partialCodestreams) {\n    codestream.set(partial, position);\n    position += partial.length;\n  }\n  return codestream;\n}\nvar JXL = {\n  validate: (input) => {\n    const boxType = toUTF8String(input, 4, 8);\n    if (boxType !== \"JXL \") return false;\n    const ftypBox = findBox(input, \"ftyp\", 0);\n    if (!ftypBox) return false;\n    const brand = toUTF8String(input, ftypBox.offset + 8, ftypBox.offset + 12);\n    return brand === \"jxl \";\n  },\n  calculate(input) {\n    const codestream = extractCodestream(input);\n    if (codestream) return JXLStream.calculate(codestream);\n    throw new Error(\"No codestream found in JXL container\");\n  }\n};\n\n// lib/types/ktx.ts\nvar KTX = {\n  validate: (input) => {\n    const signature = toUTF8String(input, 1, 7);\n    return [\"KTX 11\", \"KTX 20\"].includes(signature);\n  },\n  calculate: (input) => {\n    const type = input[5] === 49 ? \"ktx\" : \"ktx2\";\n    const offset = type === \"ktx\" ? 36 : 20;\n    return {\n      height: readUInt32LE(input, offset + 4),\n      width: readUInt32LE(input, offset),\n      type\n    };\n  }\n};\n\n// lib/types/png.ts\nvar pngSignature = \"PNG\\r\\n\u001a\\n\";\nvar pngImageHeaderChunkName = \"IHDR\";\nvar pngFriedChunkName = \"CgBI\";\nvar PNG = {\n  validate(input) {\n    if (pngSignature === toUTF8String(input, 1, 8)) {\n      let chunkName = toUTF8String(input, 12, 16);\n      if (chunkName === pngFriedChunkName) {\n        chunkName = toUTF8String(input, 28, 32);\n      }\n      if (chunkName !== pngImageHeaderChunkName) {\n        throw new TypeError(\"Invalid PNG\");\n      }\n      return true;\n    }\n    return false;\n  },\n  calculate(input) {\n    if (toUTF8String(input, 12, 16) === pngFriedChunkName) {\n      return {\n        height: readUInt32BE(input, 36),\n        width: readUInt32BE(input, 32)\n      };\n    }\n    return {\n      height: readUInt32BE(input, 20),\n      width: readUInt32BE(input, 16)\n    };\n  }\n};\n\n// lib/types/pnm.ts\nvar PNMTypes = {\n  P1: \"pbm/ascii\",\n  P2: \"pgm/ascii\",\n  P3: \"ppm/ascii\",\n  P4: \"pbm\",\n  P5: \"pgm\",\n  P6: \"ppm\",\n  P7: \"pam\",\n  PF: \"pfm\"\n};\nvar handlers = {\n  default: (lines) => {\n    let dimensions = [];\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line[0] === \"#\") {\n        continue;\n      }\n      dimensions = line.split(\" \");\n      break;\n    }\n    if (dimensions.length === 2) {\n      return {\n        height: Number.parseInt(dimensions[1], 10),\n        width: Number.parseInt(dimensions[0], 10)\n      };\n    }\n    throw new TypeError(\"Invalid PNM\");\n  },\n  pam: (lines) => {\n    const size = {};\n    while (lines.length > 0) {\n      const line = lines.shift();\n      if (line.length > 16 || line.charCodeAt(0) > 128) {\n        continue;\n      }\n      const [key, value] = line.split(\" \");\n      if (key && value) {\n        size[key.toLowerCase()] = Number.parseInt(value, 10);\n      }\n      if (size.height && size.width) {\n        break;\n      }\n    }\n    if (size.height && size.width) {\n      return {\n        height: size.height,\n        width: size.width\n      };\n    }\n    throw new TypeError(\"Invalid PAM\");\n  }\n};\nvar PNM = {\n  validate: (input) => toUTF8String(input, 0, 2) in PNMTypes,\n  calculate(input) {\n    const signature = toUTF8String(input, 0, 2);\n    const type = PNMTypes[signature];\n    const lines = toUTF8String(input, 3).split(/[\\r\\n]+/);\n    const handler = handlers[type] || handlers.default;\n    return handler(lines);\n  }\n};\n\n// lib/types/psd.ts\nvar PSD = {\n  validate: (input) => toUTF8String(input, 0, 4) === \"8BPS\",\n  calculate: (input) => ({\n    height: readUInt32BE(input, 14),\n    width: readUInt32BE(input, 18)\n  })\n};\n\n// lib/types/svg.ts\nvar svgReg = /<svg\\s([^>\"']|\"[^\"]*\"|'[^']*')*>/;\nvar extractorRegExps = {\n  height: /\\sheight=(['\"])([^%]+?)\\1/,\n  root: svgReg,\n  viewbox: /\\sviewBox=(['\"])(.+?)\\1/i,\n  width: /\\swidth=(['\"])([^%]+?)\\1/\n};\nvar INCH_CM = 2.54;\nvar units = {\n  in: 96,\n  cm: 96 / INCH_CM,\n  em: 16,\n  ex: 8,\n  m: 96 / INCH_CM * 100,\n  mm: 96 / INCH_CM / 10,\n  pc: 96 / 72 / 12,\n  pt: 96 / 72,\n  px: 1\n};\nvar unitsReg = new RegExp(\n  `^([0-9.]+(?:e\\\\d+)?)(${Object.keys(units).join(\"|\")})?$`\n);\nfunction parseLength(len) {\n  const m = unitsReg.exec(len);\n  if (!m) {\n    return void 0;\n  }\n  return Math.round(Number(m[1]) * (units[m[2]] || 1));\n}\nfunction parseViewbox(viewbox) {\n  const bounds = viewbox.split(\" \");\n  return {\n    height: parseLength(bounds[3]),\n    width: parseLength(bounds[2])\n  };\n}\nfunction parseAttributes(root) {\n  const width = root.match(extractorRegExps.width);\n  const height = root.match(extractorRegExps.height);\n  const viewbox = root.match(extractorRegExps.viewbox);\n  return {\n    height: height && parseLength(height[2]),\n    viewbox: viewbox && parseViewbox(viewbox[2]),\n    width: width && parseLength(width[2])\n  };\n}\nfunction calculateByDimensions(attrs) {\n  return {\n    height: attrs.height,\n    width: attrs.width\n  };\n}\nfunction calculateByViewbox(attrs, viewbox) {\n  const ratio = viewbox.width / viewbox.height;\n  if (attrs.width) {\n    return {\n      height: Math.floor(attrs.width / ratio),\n      width: attrs.width\n    };\n  }\n  if (attrs.height) {\n    return {\n      height: attrs.height,\n      width: Math.floor(attrs.height * ratio)\n    };\n  }\n  return {\n    height: viewbox.height,\n    width: viewbox.width\n  };\n}\nvar SVG = {\n  // Scan only the first kilo-byte to speed up the check on larger files\n  validate: (input) => svgReg.test(toUTF8String(input, 0, 1e3)),\n  calculate(input) {\n    const root = toUTF8String(input).match(extractorRegExps.root);\n    if (root) {\n      const attrs = parseAttributes(root[0]);\n      if (attrs.width && attrs.height) {\n        return calculateByDimensions(attrs);\n      }\n      if (attrs.viewbox) {\n        return calculateByViewbox(attrs, attrs.viewbox);\n      }\n    }\n    throw new TypeError(\"Invalid SVG\");\n  }\n};\n\n// lib/types/tga.ts\nvar TGA = {\n  validate(input) {\n    return readUInt16LE(input, 0) === 0 && readUInt16LE(input, 4) === 0;\n  },\n  calculate(input) {\n    return {\n      height: readUInt16LE(input, 14),\n      width: readUInt16LE(input, 12)\n    };\n  }\n};\n\n// lib/types/tiff.ts\nvar CONSTANTS = {\n  TAG: {\n    WIDTH: 256,\n    HEIGHT: 257,\n    COMPRESSION: 259\n  },\n  TYPE: {\n    SHORT: 3,\n    LONG: 4,\n    LONG8: 16\n  },\n  ENTRY_SIZE: {\n    STANDARD: 12,\n    BIG: 20\n  },\n  COUNT_SIZE: {\n    STANDARD: 2,\n    BIG: 8\n  }\n};\nfunction readIFD(input, { isBigEndian, isBigTiff }) {\n  const ifdOffset = isBigTiff ? Number(readUInt64(input, 8, isBigEndian)) : readUInt(input, 32, 4, isBigEndian);\n  const entryCountSize = isBigTiff ? CONSTANTS.COUNT_SIZE.BIG : CONSTANTS.COUNT_SIZE.STANDARD;\n  return input.slice(ifdOffset + entryCountSize);\n}\nfunction readTagValue(input, type, offset, isBigEndian) {\n  switch (type) {\n    case CONSTANTS.TYPE.SHORT:\n      return readUInt(input, 16, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG:\n      return readUInt(input, 32, offset, isBigEndian);\n    case CONSTANTS.TYPE.LONG8: {\n      const value = Number(readUInt64(input, offset, isBigEndian));\n      if (value > Number.MAX_SAFE_INTEGER) {\n        throw new TypeError(\"Value too large\");\n      }\n      return value;\n    }\n    default:\n      return 0;\n  }\n}\nfunction nextTag(input, isBigTiff) {\n  const entrySize = isBigTiff ? CONSTANTS.ENTRY_SIZE.BIG : CONSTANTS.ENTRY_SIZE.STANDARD;\n  if (input.length > entrySize) {\n    return input.slice(entrySize);\n  }\n}\nfunction extractTags(input, { isBigEndian, isBigTiff }) {\n  const tags = {};\n  let temp = input;\n  while (temp?.length) {\n    const code = readUInt(temp, 16, 0, isBigEndian);\n    const type = readUInt(temp, 16, 2, isBigEndian);\n    const length = isBigTiff ? Number(readUInt64(temp, 4, isBigEndian)) : readUInt(temp, 32, 4, isBigEndian);\n    if (code === 0) break;\n    if (length === 1 && (type === CONSTANTS.TYPE.SHORT || type === CONSTANTS.TYPE.LONG || isBigTiff && type === CONSTANTS.TYPE.LONG8)) {\n      const valueOffset = isBigTiff ? 12 : 8;\n      tags[code] = readTagValue(temp, type, valueOffset, isBigEndian);\n    }\n    temp = nextTag(temp, isBigTiff);\n  }\n  return tags;\n}\nfunction determineFormat(input) {\n  const signature = toUTF8String(input, 0, 2);\n  const version = readUInt(input, 16, 2, signature === \"MM\");\n  return {\n    isBigEndian: signature === \"MM\",\n    isBigTiff: version === 43\n  };\n}\nfunction validateBigTIFFHeader(input, isBigEndian) {\n  const byteSize = readUInt(input, 16, 4, isBigEndian);\n  const reserved = readUInt(input, 16, 6, isBigEndian);\n  if (byteSize !== 8 || reserved !== 0) {\n    throw new TypeError(\"Invalid BigTIFF header\");\n  }\n}\nvar signatures = /* @__PURE__ */ new Set([\n  \"49492a00\",\n  // Little Endian\n  \"4d4d002a\",\n  // Big Endian\n  \"49492b00\",\n  // BigTIFF Little Endian\n  \"4d4d002b\"\n  // BigTIFF Big Endian\n]);\nvar TIFF = {\n  validate: (input) => {\n    const signature = toHexString(input, 0, 4);\n    return signatures.has(signature);\n  },\n  calculate(input) {\n    const format = determineFormat(input);\n    if (format.isBigTiff) {\n      validateBigTIFFHeader(input, format.isBigEndian);\n    }\n    const ifdBuffer = readIFD(input, format);\n    const tags = extractTags(ifdBuffer, format);\n    const info = {\n      height: tags[CONSTANTS.TAG.HEIGHT],\n      width: tags[CONSTANTS.TAG.WIDTH],\n      type: format.isBigTiff ? \"bigtiff\" : \"tiff\"\n    };\n    if (tags[CONSTANTS.TAG.COMPRESSION]) {\n      info.compression = tags[CONSTANTS.TAG.COMPRESSION];\n    }\n    if (!info.width || !info.height) {\n      throw new TypeError(\"Invalid Tiff. Missing tags\");\n    }\n    return info;\n  }\n};\n\n// lib/types/webp.ts\nfunction calculateExtended(input) {\n  return {\n    height: 1 + readUInt24LE(input, 7),\n    width: 1 + readUInt24LE(input, 4)\n  };\n}\nfunction calculateLossless(input) {\n  return {\n    height: 1 + ((input[4] & 15) << 10 | input[3] << 2 | (input[2] & 192) >> 6),\n    width: 1 + ((input[2] & 63) << 8 | input[1])\n  };\n}\nfunction calculateLossy(input) {\n  return {\n    height: readInt16LE(input, 8) & 16383,\n    width: readInt16LE(input, 6) & 16383\n  };\n}\nvar WEBP = {\n  validate(input) {\n    const riffHeader = \"RIFF\" === toUTF8String(input, 0, 4);\n    const webpHeader = \"WEBP\" === toUTF8String(input, 8, 12);\n    const vp8Header = \"VP8\" === toUTF8String(input, 12, 15);\n    return riffHeader && webpHeader && vp8Header;\n  },\n  calculate(_input) {\n    const chunkHeader = toUTF8String(_input, 12, 16);\n    const input = _input.slice(20, 30);\n    if (chunkHeader === \"VP8X\") {\n      const extendedHeader = input[0];\n      const validStart = (extendedHeader & 192) === 0;\n      const validEnd = (extendedHeader & 1) === 0;\n      if (validStart && validEnd) {\n        return calculateExtended(input);\n      }\n      throw new TypeError(\"Invalid WebP\");\n    }\n    if (chunkHeader === \"VP8 \" && input[0] !== 47) {\n      return calculateLossy(input);\n    }\n    const signature = toHexString(input, 3, 6);\n    if (chunkHeader === \"VP8L\" && signature !== \"9d012a\") {\n      return calculateLossless(input);\n    }\n    throw new TypeError(\"Invalid WebP\");\n  }\n};\n\n// lib/types/index.ts\nvar typeHandlers = /* @__PURE__ */ new Map([\n  [\"bmp\", BMP],\n  [\"cur\", CUR],\n  [\"dds\", DDS],\n  [\"gif\", GIF],\n  [\"heif\", HEIF],\n  [\"icns\", ICNS],\n  [\"ico\", ICO],\n  [\"j2c\", J2C],\n  [\"jp2\", JP2],\n  [\"jpg\", JPG],\n  [\"jxl\", JXL],\n  [\"jxl-stream\", JXLStream],\n  [\"ktx\", KTX],\n  [\"png\", PNG],\n  [\"pnm\", PNM],\n  [\"psd\", PSD],\n  [\"svg\", SVG],\n  [\"tga\", TGA],\n  [\"tiff\", TIFF],\n  [\"webp\", WEBP]\n]);\nvar types = Array.from(typeHandlers.keys());\n\n// lib/detector.ts\nvar firstBytes = /* @__PURE__ */ new Map([\n  [0, \"heif\"],\n  [56, \"psd\"],\n  [66, \"bmp\"],\n  [68, \"dds\"],\n  [71, \"gif\"],\n  [73, \"tiff\"],\n  [77, \"tiff\"],\n  [82, \"webp\"],\n  [105, \"icns\"],\n  [137, \"png\"],\n  [255, \"jpg\"]\n]);\nfunction detector(input) {\n  const byte = input[0];\n  const type = firstBytes.get(byte);\n  if (type && typeHandlers.get(type).validate(input)) {\n    return type;\n  }\n  return types.find((type2) => typeHandlers.get(type2).validate(input));\n}\n\n// lib/lookup.ts\nvar globalOptions = {\n  disabledTypes: []\n};\nfunction imageSize(input) {\n  const type = detector(input);\n  if (typeof type !== \"undefined\") {\n    if (globalOptions.disabledTypes.indexOf(type) > -1) {\n      throw new TypeError(`disabled file type: ${type}`);\n    }\n    const size = typeHandlers.get(type).calculate(input);\n    if (size !== void 0) {\n      size.type = size.type ?? type;\n      if (size.images && size.images.length > 1) {\n        const largestImage = size.images.reduce((largest, current) => {\n          return current.width * current.height > largest.width * largest.height ? current : largest;\n        }, size.images[0]);\n        size.width = largestImage.width;\n        size.height = largestImage.height;\n      }\n      return size;\n    }\n  }\n  throw new TypeError(`unsupported file type: ${type}`);\n}\nvar disableTypes = (types2) => {\n  globalOptions.disabledTypes = types2;\n};\n\nexport { imageSize as default, disableTypes, imageSize, types };\n"], "mappings": ";;;AACA,IAAI,UAAU,IAAI,YAAY;AAC9B,IAAI,eAAe,CAAC,OAAO,QAAQ,GAAG,MAAM,MAAM,WAAW,QAAQ,OAAO,MAAM,MAAM,OAAO,GAAG,CAAC;AACnG,IAAI,cAAc,CAAC,OAAO,QAAQ,GAAG,MAAM,MAAM,WAAW,MAAM,MAAM,OAAO,GAAG,EAAE,OAAO,CAAC,MAAM,MAAM,OAAO,IAAI,EAAE,SAAS,EAAE,CAAC,GAAG,MAAM,EAAE,GAAG,EAAE;AACjJ,IAAI,UAAU,CAAC,OAAO,WAAW,IAAI,SAAS,MAAM,QAAQ,MAAM,aAAa,MAAM;AACrF,IAAI,cAAc,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,SAAS,GAAG,IAAI;AAChF,IAAI,eAAe,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,UAAU,GAAG,KAAK;AACnF,IAAI,eAAe,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,UAAU,GAAG,IAAI;AAClF,IAAI,eAAe,CAAC,OAAO,SAAS,MAAM;AACxC,QAAM,OAAO,QAAQ,OAAO,MAAM;AAClC,SAAO,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK;AACxD;AACA,IAAI,cAAc,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,SAAS,GAAG,IAAI;AAChF,IAAI,eAAe,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,UAAU,GAAG,KAAK;AACnF,IAAI,eAAe,CAAC,OAAO,SAAS,MAAM,QAAQ,OAAO,MAAM,EAAE,UAAU,GAAG,IAAI;AAClF,IAAI,aAAa,CAAC,OAAO,QAAQ,gBAAgB,QAAQ,OAAO,MAAM,EAAE,aAAa,GAAG,CAAC,WAAW;AACpG,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,SAAS,OAAO,MAAM,SAAS,GAAG,cAAc,OAAO;AAC9D,QAAM,SAAS,cAAc,OAAO;AACpC,QAAM,aAAa,WAAW,IAAI,GAAG,MAAM;AAC3C,SAAO,QAAQ,UAAU,EAAE,OAAO,MAAM;AAC1C;AACA,SAAS,QAAQ,OAAO,QAAQ;AAC9B,MAAI,MAAM,SAAS,SAAS,EAAG;AAC/B,QAAM,UAAU,aAAa,OAAO,MAAM;AAC1C,MAAI,MAAM,SAAS,SAAS,QAAS;AACrC,SAAO;AAAA,IACL,MAAM,aAAa,OAAO,IAAI,QAAQ,IAAI,MAAM;AAAA,IAChD;AAAA,IACA,MAAM;AAAA,EACR;AACF;AACA,SAAS,QAAQ,OAAO,SAAS,eAAe;AAC9C,SAAO,gBAAgB,MAAM,QAAQ;AACnC,UAAM,MAAM,QAAQ,OAAO,aAAa;AACxC,QAAI,CAAC,IAAK;AACV,QAAI,IAAI,SAAS,QAAS,QAAO;AACjC,qBAAiB,IAAI,OAAO,IAAI,IAAI,OAAO;AAAA,EAC7C;AACF;AAGA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,aAAa,OAAO,GAAG,CAAC,MAAM;AAAA,EACnD,WAAW,CAAC,WAAW;AAAA,IACrB,QAAQ,KAAK,IAAI,YAAY,OAAO,EAAE,CAAC;AAAA,IACvC,OAAO,aAAa,OAAO,EAAE;AAAA,EAC/B;AACF;AAGA,IAAI,YAAY;AAChB,IAAI,cAAc,IAAI,IAAI;AAC1B,IAAI,mBAAmB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACnD,SAAS,kBAAkB,OAAO,QAAQ;AACxC,QAAM,QAAQ,MAAM,MAAM;AAC1B,SAAO,UAAU,IAAI,MAAM;AAC7B;AACA,SAAS,aAAa,OAAO,YAAY;AACvC,QAAM,SAAS,cAAc,aAAa;AAC1C,SAAO;AAAA,IACL,QAAQ,kBAAkB,OAAO,SAAS,CAAC;AAAA,IAC3C,OAAO,kBAAkB,OAAO,MAAM;AAAA,EACxC;AACF;AACA,IAAI,MAAM;AAAA,EACR,SAAS,OAAO;AACd,UAAM,WAAW,aAAa,OAAO,CAAC;AACtC,UAAM,aAAa,aAAa,OAAO,CAAC;AACxC,QAAI,aAAa,KAAK,eAAe,EAAG,QAAO;AAC/C,UAAM,YAAY,aAAa,OAAO,CAAC;AACvC,WAAO,cAAc;AAAA,EACvB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,WAAW,aAAa,OAAO,CAAC;AACtC,UAAM,aAAa,aAAa,OAAO,CAAC;AACxC,QAAI,aAAa,EAAG,QAAO;AAC3B,UAAM,SAAS,CAAC;AAChB,aAAS,aAAa,GAAG,aAAa,UAAU,cAAc,GAAG;AAC/D,aAAO,KAAK,aAAa,OAAO,UAAU,CAAC;AAAA,IAC7C;AACA,WAAO;AAAA,MACL,OAAO,WAAW;AAAA,MAClB,QAAQ,WAAW;AAAA,MACnB;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,cAAc;AAClB,IAAI,MAAM;AAAA,EACR,SAAS,OAAO;AACd,UAAM,WAAW,aAAa,OAAO,CAAC;AACtC,UAAM,aAAa,aAAa,OAAO,CAAC;AACxC,QAAI,aAAa,KAAK,eAAe,EAAG,QAAO;AAC/C,UAAM,YAAY,aAAa,OAAO,CAAC;AACvC,WAAO,cAAc;AAAA,EACvB;AAAA,EACA,WAAW,CAAC,UAAU,IAAI,UAAU,KAAK;AAC3C;AAGA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,aAAa,OAAO,CAAC,MAAM;AAAA,EAChD,WAAW,CAAC,WAAW;AAAA,IACrB,QAAQ,aAAa,OAAO,EAAE;AAAA,IAC9B,OAAO,aAAa,OAAO,EAAE;AAAA,EAC/B;AACF;AAGA,IAAI,YAAY;AAChB,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,UAAU,KAAK,aAAa,OAAO,GAAG,CAAC,CAAC;AAAA,EAC7D,WAAW,CAAC,WAAW;AAAA,IACrB,QAAQ,aAAa,OAAO,CAAC;AAAA,IAC7B,OAAO,aAAa,OAAO,CAAC;AAAA,EAC9B;AACF;AAGA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA;AAER;AACA,IAAI,OAAO;AAAA,EACT,SAAS,OAAO;AACd,UAAM,UAAU,aAAa,OAAO,GAAG,CAAC;AACxC,QAAI,YAAY,OAAQ,QAAO;AAC/B,UAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,QAAI,CAAC,QAAS,QAAO;AACrB,UAAM,QAAQ,aAAa,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,EAAE;AACzE,WAAO,SAAS;AAAA,EAClB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,UAAM,UAAU,WAAW,QAAQ,OAAO,QAAQ,QAAQ,SAAS,EAAE;AACrE,UAAM,UAAU,WAAW,QAAQ,OAAO,QAAQ,QAAQ,SAAS,CAAC;AACpE,QAAI,CAAC,SAAS;AACZ,YAAM,IAAI,UAAU,iCAAiC;AAAA,IACvD;AACA,UAAM,OAAO,aAAa,OAAO,GAAG,EAAE;AACtC,UAAM,SAAS,CAAC;AAChB,QAAI,gBAAgB,QAAQ,SAAS;AACrC,WAAO,gBAAgB,QAAQ,SAAS,QAAQ,MAAM;AACpD,YAAM,UAAU,QAAQ,OAAO,QAAQ,aAAa;AACpD,UAAI,CAAC,QAAS;AACd,YAAM,WAAW,aAAa,OAAO,QAAQ,SAAS,EAAE;AACxD,YAAM,YAAY,aAAa,OAAO,QAAQ,SAAS,EAAE;AACzD,YAAM,UAAU,QAAQ,OAAO,QAAQ,aAAa;AACpD,UAAI,QAAQ;AACZ,UAAI,SAAS;AACb,UAAI,WAAW,QAAQ,SAAS,QAAQ,SAAS,QAAQ,MAAM;AAC7D,cAAM,YAAY,aAAa,OAAO,QAAQ,SAAS,EAAE;AACzD,gBAAQ,WAAW;AAAA,MACrB;AACA,aAAO,KAAK,EAAE,QAAQ,MAAM,CAAC;AAC7B,sBAAgB,QAAQ,SAAS,QAAQ;AAAA,IAC3C;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,IAAI,UAAU,8BAA8B;AAAA,IACpD;AACA,WAAO;AAAA,MACL,OAAO,OAAO,CAAC,EAAE;AAAA,MACjB,QAAQ,OAAO,CAAC,EAAE;AAAA,MAClB;AAAA,MACA,GAAG,OAAO,SAAS,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AAGA,IAAI,eAAe,IAAI;AACvB,IAAI,qBAAqB;AACzB,IAAI,sBAAsB;AAC1B,IAAI,iBAAiB;AAAA,EACnB,MAAM;AAAA,EACN,QAAQ;AAAA;AAAA,EAER,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AAAA,EACN,MAAM;AAAA;AAAA,EAEN,MAAM;AACR;AACA,SAAS,gBAAgB,OAAO,aAAa;AAC3C,QAAM,oBAAoB,cAAc;AACxC,SAAO;AAAA,IACL,aAAa,OAAO,aAAa,iBAAiB;AAAA,IAClD,aAAa,OAAO,iBAAiB;AAAA,EACvC;AACF;AACA,SAAS,cAAc,MAAM;AAC3B,QAAM,OAAO,eAAe,IAAI;AAChC,SAAO,EAAE,OAAO,MAAM,QAAQ,MAAM,KAAK;AAC3C;AACA,IAAI,OAAO;AAAA,EACT,UAAU,CAAC,UAAU,aAAa,OAAO,GAAG,CAAC,MAAM;AAAA,EACnD,UAAU,OAAO;AACf,UAAM,cAAc,MAAM;AAC1B,UAAM,aAAa,aAAa,OAAO,kBAAkB;AACzD,QAAI,cAAc;AAClB,UAAM,SAAS,CAAC;AAChB,WAAO,cAAc,cAAc,cAAc,aAAa;AAC5D,YAAM,cAAc,gBAAgB,OAAO,WAAW;AACtD,YAAM,aAAa,cAAc,YAAY,CAAC,CAAC;AAC/C,aAAO,KAAK,UAAU;AACtB,qBAAe,YAAY,CAAC;AAAA,IAC9B;AACA,QAAI,OAAO,WAAW,GAAG;AACvB,YAAM,IAAI,UAAU,8BAA8B;AAAA,IACpD;AACA,WAAO;AAAA,MACL,OAAO,OAAO,CAAC,EAAE;AAAA,MACjB,QAAQ,OAAO,CAAC,EAAE;AAAA,MAClB,GAAG,OAAO,SAAS,IAAI,EAAE,OAAO,IAAI,CAAC;AAAA,IACvC;AAAA,EACF;AACF;AAGA,IAAI,MAAM;AAAA;AAAA,EAER,UAAU,CAAC,UAAU,aAAa,OAAO,CAAC,MAAM;AAAA,EAChD,WAAW,CAAC,WAAW;AAAA,IACrB,QAAQ,aAAa,OAAO,EAAE;AAAA,IAC9B,OAAO,aAAa,OAAO,CAAC;AAAA,EAC9B;AACF;AAGA,IAAI,MAAM;AAAA,EACR,SAAS,OAAO;AACd,UAAM,UAAU,aAAa,OAAO,GAAG,CAAC;AACxC,QAAI,YAAY,OAAQ,QAAO;AAC/B,UAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,QAAI,CAAC,QAAS,QAAO;AACrB,UAAM,QAAQ,aAAa,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,EAAE;AACzE,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,UAAM,UAAU,WAAW,QAAQ,OAAO,QAAQ,QAAQ,SAAS,CAAC;AACpE,QAAI,SAAS;AACX,aAAO;AAAA,QACL,QAAQ,aAAa,OAAO,QAAQ,SAAS,CAAC;AAAA,QAC9C,OAAO,aAAa,OAAO,QAAQ,SAAS,EAAE;AAAA,MAChD;AAAA,IACF;AACA,UAAM,IAAI,UAAU,8BAA8B;AAAA,EACpD;AACF;AAGA,IAAI,cAAc;AAClB,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,IAAI,wBAAwB;AAC5B,IAAI,wBAAwB;AAC5B,IAAI,2BAA2B;AAC/B,IAAI,kBAAkB;AACtB,IAAI,8BAA8B;AAClC,SAAS,OAAO,OAAO;AACrB,SAAO,YAAY,OAAO,GAAG,CAAC,MAAM;AACtC;AACA,SAAS,YAAY,OAAO,OAAO;AACjC,SAAO;AAAA,IACL,QAAQ,aAAa,OAAO,KAAK;AAAA,IACjC,OAAO,aAAa,OAAO,QAAQ,CAAC;AAAA,EACtC;AACF;AACA,SAAS,mBAAmB,WAAW,aAAa;AAClD,QAAM,YAAY;AAClB,QAAM,SAAS,oBAAoB;AACnC,QAAM,sBAAsB,SAAS,WAAW,IAAI,QAAQ,WAAW;AACvE,WAAS,uBAAuB,GAAG,uBAAuB,qBAAqB,wBAAwB;AACrG,UAAM,QAAQ,SAAS,8BAA8B,uBAAuB;AAC5E,UAAM,MAAM,QAAQ;AACpB,QAAI,QAAQ,UAAU,QAAQ;AAC5B;AAAA,IACF;AACA,UAAM,QAAQ,UAAU,MAAM,OAAO,GAAG;AACxC,UAAM,YAAY,SAAS,OAAO,IAAI,GAAG,WAAW;AACpD,QAAI,cAAc,KAAK;AACrB,YAAM,aAAa,SAAS,OAAO,IAAI,GAAG,WAAW;AACrD,UAAI,eAAe,GAAG;AACpB;AAAA,MACF;AACA,YAAM,qBAAqB,SAAS,OAAO,IAAI,GAAG,WAAW;AAC7D,UAAI,uBAAuB,GAAG;AAC5B;AAAA,MACF;AACA,aAAO,SAAS,OAAO,IAAI,GAAG,WAAW;AAAA,IAC3C;AAAA,EACF;AACF;AACA,SAAS,kBAAkB,OAAO,OAAO;AACvC,QAAM,YAAY,MAAM,MAAM,sBAAsB,KAAK;AACzD,QAAM,YAAY;AAAA,IAChB;AAAA,IACA;AAAA,IACA,oBAAoB;AAAA,EACtB;AACA,QAAM,cAAc,cAAc;AAClC,QAAM,iBAAiB,cAAc;AACrC,MAAI,eAAe,gBAAgB;AACjC,WAAO,mBAAmB,WAAW,WAAW;AAAA,EAClD;AACF;AACA,SAAS,cAAc,OAAO,OAAO;AACnC,MAAI,QAAQ,MAAM,QAAQ;AACxB,UAAM,IAAI,UAAU,qCAAqC;AAAA,EAC3D;AACF;AACA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,YAAY,OAAO,GAAG,CAAC,MAAM;AAAA,EAClD,UAAU,QAAQ;AAChB,QAAI,QAAQ,OAAO,MAAM,CAAC;AAC1B,QAAI;AACJ,QAAI;AACJ,WAAO,MAAM,QAAQ;AACnB,YAAM,IAAI,aAAa,OAAO,CAAC;AAC/B,oBAAc,OAAO,CAAC;AACtB,UAAI,MAAM,CAAC,MAAM,KAAK;AACpB,gBAAQ,MAAM,MAAM,CAAC;AACrB;AAAA,MACF;AACA,UAAI,OAAO,KAAK,GAAG;AACjB,sBAAc,kBAAkB,OAAO,CAAC;AAAA,MAC1C;AACA,aAAO,MAAM,IAAI,CAAC;AAClB,UAAI,SAAS,OAAO,SAAS,OAAO,SAAS,KAAK;AAChD,cAAM,OAAO,YAAY,OAAO,IAAI,CAAC;AACrC,YAAI,CAAC,aAAa;AAChB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,UACL,QAAQ,KAAK;AAAA,UACb;AAAA,UACA,OAAO,KAAK;AAAA,QACd;AAAA,MACF;AACA,cAAQ,MAAM,MAAM,IAAI,CAAC;AAAA,IAC3B;AACA,UAAM,IAAI,UAAU,4BAA4B;AAAA,EAClD;AACF;AAGA,IAAI,YAAY,MAAM;AAAA,EACpB,YAAY,OAAO,YAAY;AAC7B,SAAK,QAAQ;AACb,SAAK,aAAa;AAElB,SAAK,aAAa;AAClB,SAAK,YAAY;AAAA,EACnB;AAAA;AAAA,EAEA,QAAQ,SAAS,GAAG;AAClB,QAAI,SAAS;AACb,QAAI,WAAW;AACf,WAAO,WAAW,QAAQ;AACxB,UAAI,KAAK,cAAc,KAAK,MAAM,QAAQ;AACxC,cAAM,IAAI,MAAM,sBAAsB;AAAA,MACxC;AACA,YAAM,cAAc,KAAK,MAAM,KAAK,UAAU;AAC9C,YAAM,WAAW,IAAI,KAAK;AAC1B,YAAM,aAAa,KAAK,IAAI,SAAS,UAAU,QAAQ;AACvD,UAAI,KAAK,eAAe,iBAAiB;AACvC,cAAM,QAAQ,KAAK,cAAc;AACjC,cAAM,OAAO,eAAe,KAAK,YAAY;AAC7C,kBAAU,QAAQ;AAAA,MACpB,OAAO;AACL,cAAM,QAAQ,KAAK,cAAc,KAAK,IAAI,KAAK,YAAY;AAC3D,cAAM,QAAQ,cAAc,SAAS,IAAI,KAAK,YAAY;AAC1D,iBAAS,UAAU,aAAa;AAAA,MAClC;AACA,kBAAY;AACZ,WAAK,aAAa;AAClB,UAAI,KAAK,cAAc,GAAG;AACxB,aAAK;AACL,aAAK,YAAY;AAAA,MACnB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,wBAAwB,QAAQ,cAAc;AACrD,MAAI,cAAc;AAChB,WAAO,KAAK,IAAI,OAAO,QAAQ,CAAC;AAAA,EAClC;AACA,QAAM,YAAY,OAAO,QAAQ,CAAC;AAClC,QAAM,YAAY,CAAC,GAAG,IAAI,IAAI,EAAE,EAAE,SAAS;AAC3C,SAAO,IAAI,OAAO,QAAQ,SAAS;AACrC;AACA,SAAS,oBAAoB,QAAQ,cAAc,WAAW,QAAQ;AACpE,MAAI,gBAAgB,cAAc,GAAG;AACnC,WAAO,KAAK,IAAI,OAAO,QAAQ,CAAC;AAAA,EAClC;AACA,MAAI,cAAc,GAAG;AACnB,WAAO,wBAAwB,QAAQ,KAAK;AAAA,EAC9C;AACA,QAAM,eAAe,CAAC,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG,IAAI,GAAG,CAAC;AAC1D,SAAO,KAAK,MAAM,SAAS,aAAa,YAAY,CAAC,CAAC;AACxD;AACA,IAAI,YAAY;AAAA,EACd,UAAU,CAAC,UAAU;AACnB,WAAO,YAAY,OAAO,GAAG,CAAC,MAAM;AAAA,EACtC;AAAA,EACA,UAAU,OAAO;AACf,UAAM,SAAS,IAAI,UAAU,OAAO,eAAe;AACnD,UAAM,eAAe,OAAO,QAAQ,CAAC,MAAM;AAC3C,UAAM,SAAS,wBAAwB,QAAQ,YAAY;AAC3D,UAAM,YAAY,OAAO,QAAQ,CAAC;AAClC,UAAM,QAAQ,oBAAoB,QAAQ,cAAc,WAAW,MAAM;AACzE,WAAO,EAAE,OAAO,OAAO;AAAA,EACzB;AACF;AAGA,SAAS,kBAAkB,OAAO;AAChC,QAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,MAAI,SAAS;AACX,WAAO,MAAM,MAAM,QAAQ,SAAS,GAAG,QAAQ,SAAS,QAAQ,IAAI;AAAA,EACtE;AACA,QAAM,iBAAiB,sBAAsB,KAAK;AAClD,MAAI,eAAe,SAAS,GAAG;AAC7B,WAAO,uBAAuB,cAAc;AAAA,EAC9C;AACA,SAAO;AACT;AACA,SAAS,sBAAsB,OAAO;AACpC,QAAM,iBAAiB,CAAC;AACxB,MAAI,SAAS;AACb,SAAO,SAAS,MAAM,QAAQ;AAC5B,UAAM,UAAU,QAAQ,OAAO,QAAQ,MAAM;AAC7C,QAAI,CAAC,QAAS;AACd,mBAAe;AAAA,MACb,MAAM,MAAM,QAAQ,SAAS,IAAI,QAAQ,SAAS,QAAQ,IAAI;AAAA,IAChE;AACA,aAAS,QAAQ,SAAS,QAAQ;AAAA,EACpC;AACA,SAAO;AACT;AACA,SAAS,uBAAuB,oBAAoB;AAClD,QAAM,cAAc,mBAAmB;AAAA,IACrC,CAAC,KAAK,SAAS,MAAM,KAAK;AAAA,IAC1B;AAAA,EACF;AACA,QAAM,aAAa,IAAI,WAAW,WAAW;AAC7C,MAAI,WAAW;AACf,aAAW,WAAW,oBAAoB;AACxC,eAAW,IAAI,SAAS,QAAQ;AAChC,gBAAY,QAAQ;AAAA,EACtB;AACA,SAAO;AACT;AACA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU;AACnB,UAAM,UAAU,aAAa,OAAO,GAAG,CAAC;AACxC,QAAI,YAAY,OAAQ,QAAO;AAC/B,UAAM,UAAU,QAAQ,OAAO,QAAQ,CAAC;AACxC,QAAI,CAAC,QAAS,QAAO;AACrB,UAAM,QAAQ,aAAa,OAAO,QAAQ,SAAS,GAAG,QAAQ,SAAS,EAAE;AACzE,WAAO,UAAU;AAAA,EACnB;AAAA,EACA,UAAU,OAAO;AACf,UAAM,aAAa,kBAAkB,KAAK;AAC1C,QAAI,WAAY,QAAO,UAAU,UAAU,UAAU;AACrD,UAAM,IAAI,MAAM,sCAAsC;AAAA,EACxD;AACF;AAGA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU;AACnB,UAAM,YAAY,aAAa,OAAO,GAAG,CAAC;AAC1C,WAAO,CAAC,UAAU,QAAQ,EAAE,SAAS,SAAS;AAAA,EAChD;AAAA,EACA,WAAW,CAAC,UAAU;AACpB,UAAM,OAAO,MAAM,CAAC,MAAM,KAAK,QAAQ;AACvC,UAAM,SAAS,SAAS,QAAQ,KAAK;AACrC,WAAO;AAAA,MACL,QAAQ,aAAa,OAAO,SAAS,CAAC;AAAA,MACtC,OAAO,aAAa,OAAO,MAAM;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACF;AAGA,IAAI,eAAe;AACnB,IAAI,0BAA0B;AAC9B,IAAI,oBAAoB;AACxB,IAAI,MAAM;AAAA,EACR,SAAS,OAAO;AACd,QAAI,iBAAiB,aAAa,OAAO,GAAG,CAAC,GAAG;AAC9C,UAAI,YAAY,aAAa,OAAO,IAAI,EAAE;AAC1C,UAAI,cAAc,mBAAmB;AACnC,oBAAY,aAAa,OAAO,IAAI,EAAE;AAAA,MACxC;AACA,UAAI,cAAc,yBAAyB;AACzC,cAAM,IAAI,UAAU,aAAa;AAAA,MACnC;AACA,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA,EACA,UAAU,OAAO;AACf,QAAI,aAAa,OAAO,IAAI,EAAE,MAAM,mBAAmB;AACrD,aAAO;AAAA,QACL,QAAQ,aAAa,OAAO,EAAE;AAAA,QAC9B,OAAO,aAAa,OAAO,EAAE;AAAA,MAC/B;AAAA,IACF;AACA,WAAO;AAAA,MACL,QAAQ,aAAa,OAAO,EAAE;AAAA,MAC9B,OAAO,aAAa,OAAO,EAAE;AAAA,IAC/B;AAAA,EACF;AACF;AAGA,IAAI,WAAW;AAAA,EACb,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AACN;AACA,IAAI,WAAW;AAAA,EACb,SAAS,CAAC,UAAU;AAClB,QAAI,aAAa,CAAC;AAClB,WAAO,MAAM,SAAS,GAAG;AACvB,YAAM,OAAO,MAAM,MAAM;AACzB,UAAI,KAAK,CAAC,MAAM,KAAK;AACnB;AAAA,MACF;AACA,mBAAa,KAAK,MAAM,GAAG;AAC3B;AAAA,IACF;AACA,QAAI,WAAW,WAAW,GAAG;AAC3B,aAAO;AAAA,QACL,QAAQ,OAAO,SAAS,WAAW,CAAC,GAAG,EAAE;AAAA,QACzC,OAAO,OAAO,SAAS,WAAW,CAAC,GAAG,EAAE;AAAA,MAC1C;AAAA,IACF;AACA,UAAM,IAAI,UAAU,aAAa;AAAA,EACnC;AAAA,EACA,KAAK,CAAC,UAAU;AACd,UAAM,OAAO,CAAC;AACd,WAAO,MAAM,SAAS,GAAG;AACvB,YAAM,OAAO,MAAM,MAAM;AACzB,UAAI,KAAK,SAAS,MAAM,KAAK,WAAW,CAAC,IAAI,KAAK;AAChD;AAAA,MACF;AACA,YAAM,CAAC,KAAK,KAAK,IAAI,KAAK,MAAM,GAAG;AACnC,UAAI,OAAO,OAAO;AAChB,aAAK,IAAI,YAAY,CAAC,IAAI,OAAO,SAAS,OAAO,EAAE;AAAA,MACrD;AACA,UAAI,KAAK,UAAU,KAAK,OAAO;AAC7B;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAO;AAAA,QACL,QAAQ,KAAK;AAAA,QACb,OAAO,KAAK;AAAA,MACd;AAAA,IACF;AACA,UAAM,IAAI,UAAU,aAAa;AAAA,EACnC;AACF;AACA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,aAAa,OAAO,GAAG,CAAC,KAAK;AAAA,EAClD,UAAU,OAAO;AACf,UAAM,YAAY,aAAa,OAAO,GAAG,CAAC;AAC1C,UAAM,OAAO,SAAS,SAAS;AAC/B,UAAM,QAAQ,aAAa,OAAO,CAAC,EAAE,MAAM,SAAS;AACpD,UAAM,UAAU,SAAS,IAAI,KAAK,SAAS;AAC3C,WAAO,QAAQ,KAAK;AAAA,EACtB;AACF;AAGA,IAAI,MAAM;AAAA,EACR,UAAU,CAAC,UAAU,aAAa,OAAO,GAAG,CAAC,MAAM;AAAA,EACnD,WAAW,CAAC,WAAW;AAAA,IACrB,QAAQ,aAAa,OAAO,EAAE;AAAA,IAC9B,OAAO,aAAa,OAAO,EAAE;AAAA,EAC/B;AACF;AAGA,IAAI,SAAS;AACb,IAAI,mBAAmB;AAAA,EACrB,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,SAAS;AAAA,EACT,OAAO;AACT;AACA,IAAI,UAAU;AACd,IAAI,QAAQ;AAAA,EACV,IAAI;AAAA,EACJ,IAAI,KAAK;AAAA,EACT,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG,KAAK,UAAU;AAAA,EAClB,IAAI,KAAK,UAAU;AAAA,EACnB,IAAI,KAAK,KAAK;AAAA,EACd,IAAI,KAAK;AAAA,EACT,IAAI;AACN;AACA,IAAI,WAAW,IAAI;AAAA,EACjB,wBAAwB,OAAO,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC;AACtD;AACA,SAAS,YAAY,KAAK;AACxB,QAAM,IAAI,SAAS,KAAK,GAAG;AAC3B,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AACA,SAAO,KAAK,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE,CAAC,CAAC,KAAK,EAAE;AACrD;AACA,SAAS,aAAa,SAAS;AAC7B,QAAM,SAAS,QAAQ,MAAM,GAAG;AAChC,SAAO;AAAA,IACL,QAAQ,YAAY,OAAO,CAAC,CAAC;AAAA,IAC7B,OAAO,YAAY,OAAO,CAAC,CAAC;AAAA,EAC9B;AACF;AACA,SAAS,gBAAgB,MAAM;AAC7B,QAAM,QAAQ,KAAK,MAAM,iBAAiB,KAAK;AAC/C,QAAM,SAAS,KAAK,MAAM,iBAAiB,MAAM;AACjD,QAAM,UAAU,KAAK,MAAM,iBAAiB,OAAO;AACnD,SAAO;AAAA,IACL,QAAQ,UAAU,YAAY,OAAO,CAAC,CAAC;AAAA,IACvC,SAAS,WAAW,aAAa,QAAQ,CAAC,CAAC;AAAA,IAC3C,OAAO,SAAS,YAAY,MAAM,CAAC,CAAC;AAAA,EACtC;AACF;AACA,SAAS,sBAAsB,OAAO;AACpC,SAAO;AAAA,IACL,QAAQ,MAAM;AAAA,IACd,OAAO,MAAM;AAAA,EACf;AACF;AACA,SAAS,mBAAmB,OAAO,SAAS;AAC1C,QAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,MAAI,MAAM,OAAO;AACf,WAAO;AAAA,MACL,QAAQ,KAAK,MAAM,MAAM,QAAQ,KAAK;AAAA,MACtC,OAAO,MAAM;AAAA,IACf;AAAA,EACF;AACA,MAAI,MAAM,QAAQ;AAChB,WAAO;AAAA,MACL,QAAQ,MAAM;AAAA,MACd,OAAO,KAAK,MAAM,MAAM,SAAS,KAAK;AAAA,IACxC;AAAA,EACF;AACA,SAAO;AAAA,IACL,QAAQ,QAAQ;AAAA,IAChB,OAAO,QAAQ;AAAA,EACjB;AACF;AACA,IAAI,MAAM;AAAA;AAAA,EAER,UAAU,CAAC,UAAU,OAAO,KAAK,aAAa,OAAO,GAAG,GAAG,CAAC;AAAA,EAC5D,UAAU,OAAO;AACf,UAAM,OAAO,aAAa,KAAK,EAAE,MAAM,iBAAiB,IAAI;AAC5D,QAAI,MAAM;AACR,YAAM,QAAQ,gBAAgB,KAAK,CAAC,CAAC;AACrC,UAAI,MAAM,SAAS,MAAM,QAAQ;AAC/B,eAAO,sBAAsB,KAAK;AAAA,MACpC;AACA,UAAI,MAAM,SAAS;AACjB,eAAO,mBAAmB,OAAO,MAAM,OAAO;AAAA,MAChD;AAAA,IACF;AACA,UAAM,IAAI,UAAU,aAAa;AAAA,EACnC;AACF;AAGA,IAAI,MAAM;AAAA,EACR,SAAS,OAAO;AACd,WAAO,aAAa,OAAO,CAAC,MAAM,KAAK,aAAa,OAAO,CAAC,MAAM;AAAA,EACpE;AAAA,EACA,UAAU,OAAO;AACf,WAAO;AAAA,MACL,QAAQ,aAAa,OAAO,EAAE;AAAA,MAC9B,OAAO,aAAa,OAAO,EAAE;AAAA,IAC/B;AAAA,EACF;AACF;AAGA,IAAI,YAAY;AAAA,EACd,KAAK;AAAA,IACH,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,aAAa;AAAA,EACf;AAAA,EACA,MAAM;AAAA,IACJ,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,UAAU;AAAA,IACV,KAAK;AAAA,EACP;AAAA,EACA,YAAY;AAAA,IACV,UAAU;AAAA,IACV,KAAK;AAAA,EACP;AACF;AACA,SAAS,QAAQ,OAAO,EAAE,aAAa,UAAU,GAAG;AAClD,QAAM,YAAY,YAAY,OAAO,WAAW,OAAO,GAAG,WAAW,CAAC,IAAI,SAAS,OAAO,IAAI,GAAG,WAAW;AAC5G,QAAM,iBAAiB,YAAY,UAAU,WAAW,MAAM,UAAU,WAAW;AACnF,SAAO,MAAM,MAAM,YAAY,cAAc;AAC/C;AACA,SAAS,aAAa,OAAO,MAAM,QAAQ,aAAa;AACtD,UAAQ,MAAM;AAAA,IACZ,KAAK,UAAU,KAAK;AAClB,aAAO,SAAS,OAAO,IAAI,QAAQ,WAAW;AAAA,IAChD,KAAK,UAAU,KAAK;AAClB,aAAO,SAAS,OAAO,IAAI,QAAQ,WAAW;AAAA,IAChD,KAAK,UAAU,KAAK,OAAO;AACzB,YAAM,QAAQ,OAAO,WAAW,OAAO,QAAQ,WAAW,CAAC;AAC3D,UAAI,QAAQ,OAAO,kBAAkB;AACnC,cAAM,IAAI,UAAU,iBAAiB;AAAA,MACvC;AACA,aAAO;AAAA,IACT;AAAA,IACA;AACE,aAAO;AAAA,EACX;AACF;AACA,SAAS,QAAQ,OAAO,WAAW;AACjC,QAAM,YAAY,YAAY,UAAU,WAAW,MAAM,UAAU,WAAW;AAC9E,MAAI,MAAM,SAAS,WAAW;AAC5B,WAAO,MAAM,MAAM,SAAS;AAAA,EAC9B;AACF;AACA,SAAS,YAAY,OAAO,EAAE,aAAa,UAAU,GAAG;AACtD,QAAM,OAAO,CAAC;AACd,MAAI,OAAO;AACX,SAAO,6BAAM,QAAQ;AACnB,UAAM,OAAO,SAAS,MAAM,IAAI,GAAG,WAAW;AAC9C,UAAM,OAAO,SAAS,MAAM,IAAI,GAAG,WAAW;AAC9C,UAAM,SAAS,YAAY,OAAO,WAAW,MAAM,GAAG,WAAW,CAAC,IAAI,SAAS,MAAM,IAAI,GAAG,WAAW;AACvG,QAAI,SAAS,EAAG;AAChB,QAAI,WAAW,MAAM,SAAS,UAAU,KAAK,SAAS,SAAS,UAAU,KAAK,QAAQ,aAAa,SAAS,UAAU,KAAK,QAAQ;AACjI,YAAM,cAAc,YAAY,KAAK;AACrC,WAAK,IAAI,IAAI,aAAa,MAAM,MAAM,aAAa,WAAW;AAAA,IAChE;AACA,WAAO,QAAQ,MAAM,SAAS;AAAA,EAChC;AACA,SAAO;AACT;AACA,SAAS,gBAAgB,OAAO;AAC9B,QAAM,YAAY,aAAa,OAAO,GAAG,CAAC;AAC1C,QAAM,UAAU,SAAS,OAAO,IAAI,GAAG,cAAc,IAAI;AACzD,SAAO;AAAA,IACL,aAAa,cAAc;AAAA,IAC3B,WAAW,YAAY;AAAA,EACzB;AACF;AACA,SAAS,sBAAsB,OAAO,aAAa;AACjD,QAAM,WAAW,SAAS,OAAO,IAAI,GAAG,WAAW;AACnD,QAAM,WAAW,SAAS,OAAO,IAAI,GAAG,WAAW;AACnD,MAAI,aAAa,KAAK,aAAa,GAAG;AACpC,UAAM,IAAI,UAAU,wBAAwB;AAAA,EAC9C;AACF;AACA,IAAI,aAA6B,oBAAI,IAAI;AAAA,EACvC;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAAA,EAEA;AAAA;AAEF,CAAC;AACD,IAAI,OAAO;AAAA,EACT,UAAU,CAAC,UAAU;AACnB,UAAM,YAAY,YAAY,OAAO,GAAG,CAAC;AACzC,WAAO,WAAW,IAAI,SAAS;AAAA,EACjC;AAAA,EACA,UAAU,OAAO;AACf,UAAM,SAAS,gBAAgB,KAAK;AACpC,QAAI,OAAO,WAAW;AACpB,4BAAsB,OAAO,OAAO,WAAW;AAAA,IACjD;AACA,UAAM,YAAY,QAAQ,OAAO,MAAM;AACvC,UAAM,OAAO,YAAY,WAAW,MAAM;AAC1C,UAAM,OAAO;AAAA,MACX,QAAQ,KAAK,UAAU,IAAI,MAAM;AAAA,MACjC,OAAO,KAAK,UAAU,IAAI,KAAK;AAAA,MAC/B,MAAM,OAAO,YAAY,YAAY;AAAA,IACvC;AACA,QAAI,KAAK,UAAU,IAAI,WAAW,GAAG;AACnC,WAAK,cAAc,KAAK,UAAU,IAAI,WAAW;AAAA,IACnD;AACA,QAAI,CAAC,KAAK,SAAS,CAAC,KAAK,QAAQ;AAC/B,YAAM,IAAI,UAAU,4BAA4B;AAAA,IAClD;AACA,WAAO;AAAA,EACT;AACF;AAGA,SAAS,kBAAkB,OAAO;AAChC,SAAO;AAAA,IACL,QAAQ,IAAI,aAAa,OAAO,CAAC;AAAA,IACjC,OAAO,IAAI,aAAa,OAAO,CAAC;AAAA,EAClC;AACF;AACA,SAAS,kBAAkB,OAAO;AAChC,SAAO;AAAA,IACL,QAAQ,MAAM,MAAM,CAAC,IAAI,OAAO,KAAK,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,IAAI,QAAQ;AAAA,IACzE,OAAO,MAAM,MAAM,CAAC,IAAI,OAAO,IAAI,MAAM,CAAC;AAAA,EAC5C;AACF;AACA,SAAS,eAAe,OAAO;AAC7B,SAAO;AAAA,IACL,QAAQ,YAAY,OAAO,CAAC,IAAI;AAAA,IAChC,OAAO,YAAY,OAAO,CAAC,IAAI;AAAA,EACjC;AACF;AACA,IAAI,OAAO;AAAA,EACT,SAAS,OAAO;AACd,UAAM,aAAa,WAAW,aAAa,OAAO,GAAG,CAAC;AACtD,UAAM,aAAa,WAAW,aAAa,OAAO,GAAG,EAAE;AACvD,UAAM,YAAY,UAAU,aAAa,OAAO,IAAI,EAAE;AACtD,WAAO,cAAc,cAAc;AAAA,EACrC;AAAA,EACA,UAAU,QAAQ;AAChB,UAAM,cAAc,aAAa,QAAQ,IAAI,EAAE;AAC/C,UAAM,QAAQ,OAAO,MAAM,IAAI,EAAE;AACjC,QAAI,gBAAgB,QAAQ;AAC1B,YAAM,iBAAiB,MAAM,CAAC;AAC9B,YAAM,cAAc,iBAAiB,SAAS;AAC9C,YAAM,YAAY,iBAAiB,OAAO;AAC1C,UAAI,cAAc,UAAU;AAC1B,eAAO,kBAAkB,KAAK;AAAA,MAChC;AACA,YAAM,IAAI,UAAU,cAAc;AAAA,IACpC;AACA,QAAI,gBAAgB,UAAU,MAAM,CAAC,MAAM,IAAI;AAC7C,aAAO,eAAe,KAAK;AAAA,IAC7B;AACA,UAAM,YAAY,YAAY,OAAO,GAAG,CAAC;AACzC,QAAI,gBAAgB,UAAU,cAAc,UAAU;AACpD,aAAO,kBAAkB,KAAK;AAAA,IAChC;AACA,UAAM,IAAI,UAAU,cAAc;AAAA,EACpC;AACF;AAGA,IAAI,eAA+B,oBAAI,IAAI;AAAA,EACzC,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,QAAQ,IAAI;AAAA,EACb,CAAC,QAAQ,IAAI;AAAA,EACb,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,cAAc,SAAS;AAAA,EACxB,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,OAAO,GAAG;AAAA,EACX,CAAC,QAAQ,IAAI;AAAA,EACb,CAAC,QAAQ,IAAI;AACf,CAAC;AACD,IAAI,QAAQ,MAAM,KAAK,aAAa,KAAK,CAAC;AAG1C,IAAI,aAA6B,oBAAI,IAAI;AAAA,EACvC,CAAC,GAAG,MAAM;AAAA,EACV,CAAC,IAAI,KAAK;AAAA,EACV,CAAC,IAAI,KAAK;AAAA,EACV,CAAC,IAAI,KAAK;AAAA,EACV,CAAC,IAAI,KAAK;AAAA,EACV,CAAC,IAAI,MAAM;AAAA,EACX,CAAC,IAAI,MAAM;AAAA,EACX,CAAC,IAAI,MAAM;AAAA,EACX,CAAC,KAAK,MAAM;AAAA,EACZ,CAAC,KAAK,KAAK;AAAA,EACX,CAAC,KAAK,KAAK;AACb,CAAC;AACD,SAAS,SAAS,OAAO;AACvB,QAAM,OAAO,MAAM,CAAC;AACpB,QAAM,OAAO,WAAW,IAAI,IAAI;AAChC,MAAI,QAAQ,aAAa,IAAI,IAAI,EAAE,SAAS,KAAK,GAAG;AAClD,WAAO;AAAA,EACT;AACA,SAAO,MAAM,KAAK,CAAC,UAAU,aAAa,IAAI,KAAK,EAAE,SAAS,KAAK,CAAC;AACtE;AAGA,IAAI,gBAAgB;AAAA,EAClB,eAAe,CAAC;AAClB;AACA,SAAS,UAAU,OAAO;AACxB,QAAM,OAAO,SAAS,KAAK;AAC3B,MAAI,OAAO,SAAS,aAAa;AAC/B,QAAI,cAAc,cAAc,QAAQ,IAAI,IAAI,IAAI;AAClD,YAAM,IAAI,UAAU,uBAAuB,IAAI,EAAE;AAAA,IACnD;AACA,UAAM,OAAO,aAAa,IAAI,IAAI,EAAE,UAAU,KAAK;AACnD,QAAI,SAAS,QAAQ;AACnB,WAAK,OAAO,KAAK,QAAQ;AACzB,UAAI,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AACzC,cAAM,eAAe,KAAK,OAAO,OAAO,CAAC,SAAS,YAAY;AAC5D,iBAAO,QAAQ,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,QAAQ,SAAS,UAAU;AAAA,QACrF,GAAG,KAAK,OAAO,CAAC,CAAC;AACjB,aAAK,QAAQ,aAAa;AAC1B,aAAK,SAAS,aAAa;AAAA,MAC7B;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACA,QAAM,IAAI,UAAU,0BAA0B,IAAI,EAAE;AACtD;AACA,IAAI,eAAe,CAAC,WAAW;AAC7B,gBAAc,gBAAgB;AAChC;", "names": []}