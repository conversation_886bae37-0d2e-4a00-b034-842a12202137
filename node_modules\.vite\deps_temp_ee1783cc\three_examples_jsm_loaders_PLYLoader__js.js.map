{"version": 3, "sources": ["../../three/examples/jsm/loaders/PLYLoader.js"], "sourcesContent": ["import {\n\t<PERSON>ufferGeometry,\n\tFileLoader,\n\tFloat32BufferAttribute,\n\tLoader,\n\tColor\n} from 'three';\n\n/**\n * Description: A THREE loader for PLY ASCII files (known as the Polygon\n * File Format or the Stanford Triangle Format).\n *\n * Limitations: ASCII decoding assumes file is UTF-8.\n *\n * Usage:\n *\tconst loader = new PLYLoader();\n *\tloader.load('./models/ply/ascii/dolphins.ply', function (geometry) {\n *\n *\t\tscene.add( new THREE.Mesh( geometry ) );\n *\n *\t} );\n *\n * If the PLY file uses non standard property names, they can be mapped while\n * loading. For example, the following maps the properties\n * “diffuse_(red|green|blue)” in the file to standard color names.\n *\n * loader.setPropertyNameMapping( {\n *\tdiffuse_red: 'red',\n *\tdiffuse_green: 'green',\n *\tdiffuse_blue: 'blue'\n * } );\n *\n * Custom properties outside of the defaults for position, uv, normal\n * and color attributes can be added using the setCustomPropertyNameMapping method.\n * For example, the following maps the element properties “custom_property_a”\n * and “custom_property_b” to an attribute “customAttribute” with an item size of 2.\n * Attribute item sizes are set from the number of element properties in the property array.\n *\n * loader.setCustomPropertyNameMapping( {\n *\tcustomAttribute: ['custom_property_a', 'custom_property_b'],\n * } );\n *\n */\n\nconst _color = new Color();\n\nclass PLYLoader extends Loader {\n\n\tconstructor( manager ) {\n\n\t\tsuper( manager );\n\n\t\tthis.propertyNameMapping = {};\n\t\tthis.customPropertyMapping = {};\n\n\t}\n\n\tload( url, onLoad, onProgress, onError ) {\n\n\t\tconst scope = this;\n\n\t\tconst loader = new FileLoader( this.manager );\n\t\tloader.setPath( this.path );\n\t\tloader.setResponseType( 'arraybuffer' );\n\t\tloader.setRequestHeader( this.requestHeader );\n\t\tloader.setWithCredentials( this.withCredentials );\n\t\tloader.load( url, function ( text ) {\n\n\t\t\ttry {\n\n\t\t\t\tonLoad( scope.parse( text ) );\n\n\t\t\t} catch ( e ) {\n\n\t\t\t\tif ( onError ) {\n\n\t\t\t\t\tonError( e );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tconsole.error( e );\n\n\t\t\t\t}\n\n\t\t\t\tscope.manager.itemError( url );\n\n\t\t\t}\n\n\t\t}, onProgress, onError );\n\n\t}\n\n\tsetPropertyNameMapping( mapping ) {\n\n\t\tthis.propertyNameMapping = mapping;\n\n\t}\n\n\tsetCustomPropertyNameMapping( mapping ) {\n\n\t\tthis.customPropertyMapping = mapping;\n\n\t}\n\n\tparse( data ) {\n\n\t\tfunction parseHeader( data, headerLength = 0 ) {\n\n\t\t\tconst patternHeader = /^ply([\\s\\S]*)end_header(\\r\\n|\\r|\\n)/;\n\t\t\tlet headerText = '';\n\t\t\tconst result = patternHeader.exec( data );\n\n\t\t\tif ( result !== null ) {\n\n\t\t\t\theaderText = result[ 1 ];\n\n\t\t\t}\n\n\t\t\tconst header = {\n\t\t\t\tcomments: [],\n\t\t\t\telements: [],\n\t\t\t\theaderLength: headerLength,\n\t\t\t\tobjInfo: ''\n\t\t\t};\n\n\t\t\tconst lines = headerText.split( /\\r\\n|\\r|\\n/ );\n\t\t\tlet currentElement;\n\n\t\t\tfunction make_ply_element_property( propertValues, propertyNameMapping ) {\n\n\t\t\t\tconst property = { type: propertValues[ 0 ] };\n\n\t\t\t\tif ( property.type === 'list' ) {\n\n\t\t\t\t\tproperty.name = propertValues[ 3 ];\n\t\t\t\t\tproperty.countType = propertValues[ 1 ];\n\t\t\t\t\tproperty.itemType = propertValues[ 2 ];\n\n\t\t\t\t} else {\n\n\t\t\t\t\tproperty.name = propertValues[ 1 ];\n\n\t\t\t\t}\n\n\t\t\t\tif ( property.name in propertyNameMapping ) {\n\n\t\t\t\t\tproperty.name = propertyNameMapping[ property.name ];\n\n\t\t\t\t}\n\n\t\t\t\treturn property;\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0; i < lines.length; i ++ ) {\n\n\t\t\t\tlet line = lines[ i ];\n\t\t\t\tline = line.trim();\n\n\t\t\t\tif ( line === '' ) continue;\n\n\t\t\t\tconst lineValues = line.split( /\\s+/ );\n\t\t\t\tconst lineType = lineValues.shift();\n\t\t\t\tline = lineValues.join( ' ' );\n\n\t\t\t\tswitch ( lineType ) {\n\n\t\t\t\t\tcase 'format':\n\n\t\t\t\t\t\theader.format = lineValues[ 0 ];\n\t\t\t\t\t\theader.version = lineValues[ 1 ];\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'comment':\n\n\t\t\t\t\t\theader.comments.push( line );\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'element':\n\n\t\t\t\t\t\tif ( currentElement !== undefined ) {\n\n\t\t\t\t\t\t\theader.elements.push( currentElement );\n\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tcurrentElement = {};\n\t\t\t\t\t\tcurrentElement.name = lineValues[ 0 ];\n\t\t\t\t\t\tcurrentElement.count = parseInt( lineValues[ 1 ] );\n\t\t\t\t\t\tcurrentElement.properties = [];\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'property':\n\n\t\t\t\t\t\tcurrentElement.properties.push( make_ply_element_property( lineValues, scope.propertyNameMapping ) );\n\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'obj_info':\n\n\t\t\t\t\t\theader.objInfo = line;\n\n\t\t\t\t\t\tbreak;\n\n\n\t\t\t\t\tdefault:\n\n\t\t\t\t\t\tconsole.log( 'unhandled', lineType, lineValues );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tif ( currentElement !== undefined ) {\n\n\t\t\t\theader.elements.push( currentElement );\n\n\t\t\t}\n\n\t\t\treturn header;\n\n\t\t}\n\n\t\tfunction parseASCIINumber( n, type ) {\n\n\t\t\tswitch ( type ) {\n\n\t\t\t\tcase 'char': case 'uchar': case 'short': case 'ushort': case 'int': case 'uint':\n\t\t\t\tcase 'int8': case 'uint8': case 'int16': case 'uint16': case 'int32': case 'uint32':\n\n\t\t\t\t\treturn parseInt( n );\n\n\t\t\t\tcase 'float': case 'double': case 'float32': case 'float64':\n\n\t\t\t\t\treturn parseFloat( n );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseASCIIElement( properties, tokens ) {\n\n\t\t\tconst element = {};\n\n\t\t\tfor ( let i = 0; i < properties.length; i ++ ) {\n\n\t\t\t\tif ( tokens.empty() ) return null;\n\n\t\t\t\tif ( properties[ i ].type === 'list' ) {\n\n\t\t\t\t\tconst list = [];\n\t\t\t\t\tconst n = parseASCIINumber( tokens.next(), properties[ i ].countType );\n\n\t\t\t\t\tfor ( let j = 0; j < n; j ++ ) {\n\n\t\t\t\t\t\tif ( tokens.empty() ) return null;\n\n\t\t\t\t\t\tlist.push( parseASCIINumber( tokens.next(), properties[ i ].itemType ) );\n\n\t\t\t\t\t}\n\n\t\t\t\t\telement[ properties[ i ].name ] = list;\n\n\t\t\t\t} else {\n\n\t\t\t\t\telement[ properties[ i ].name ] = parseASCIINumber( tokens.next(), properties[ i ].type );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn element;\n\n\t\t}\n\n\t\tfunction createBuffer() {\n\n\t\t\tconst buffer = {\n\t\t\t  indices: [],\n\t\t\t  vertices: [],\n\t\t\t  normals: [],\n\t\t\t  uvs: [],\n\t\t\t  faceVertexUvs: [],\n\t\t\t  colors: [],\n\t\t\t  faceVertexColors: []\n\t\t\t};\n\n\t\t\tfor ( const customProperty of Object.keys( scope.customPropertyMapping ) ) {\n\n\t\t\t  buffer[ customProperty ] = [];\n\n\t\t\t}\n\n\t\t\treturn buffer;\n\n\t\t}\n\n\t\tfunction mapElementAttributes( properties ) {\n\n\t\t\tconst elementNames = properties.map( property => {\n\n\t\t\t\treturn property.name;\n\n\t\t\t} );\n\n\t\t\tfunction findAttrName( names ) {\n\n\t\t\t\tfor ( let i = 0, l = names.length; i < l; i ++ ) {\n\n\t\t\t\t\tconst name = names[ i ];\n\n\t\t\t\t\tif ( elementNames.includes( name ) ) return name;\n\n\t\t\t\t}\n\n\t\t\t\treturn null;\n\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tattrX: findAttrName( [ 'x', 'px', 'posx' ] ) || 'x',\n\t\t\t\tattrY: findAttrName( [ 'y', 'py', 'posy' ] ) || 'y',\n\t\t\t\tattrZ: findAttrName( [ 'z', 'pz', 'posz' ] ) || 'z',\n\t\t\t\tattrNX: findAttrName( [ 'nx', 'normalx' ] ),\n\t\t\t\tattrNY: findAttrName( [ 'ny', 'normaly' ] ),\n\t\t\t\tattrNZ: findAttrName( [ 'nz', 'normalz' ] ),\n\t\t\t\tattrS: findAttrName( [ 's', 'u', 'texture_u', 'tx' ] ),\n\t\t\t\tattrT: findAttrName( [ 't', 'v', 'texture_v', 'ty' ] ),\n\t\t\t\tattrR: findAttrName( [ 'red', 'diffuse_red', 'r', 'diffuse_r' ] ),\n\t\t\t\tattrG: findAttrName( [ 'green', 'diffuse_green', 'g', 'diffuse_g' ] ),\n\t\t\t\tattrB: findAttrName( [ 'blue', 'diffuse_blue', 'b', 'diffuse_b' ] ),\n\t\t\t};\n\n\t\t}\n\n\t\tfunction parseASCII( data, header ) {\n\n\t\t\t// PLY ascii format specification, as per http://en.wikipedia.org/wiki/PLY_(file_format)\n\n\t\t\tconst buffer = createBuffer();\n\n\t\t\tconst patternBody = /end_header\\s+(\\S[\\s\\S]*\\S|\\S)\\s*$/;\n\t\t\tlet body, matches;\n\n\t\t\tif ( ( matches = patternBody.exec( data ) ) !== null ) {\n\n\t\t\t\tbody = matches[ 1 ].split( /\\s+/ );\n\n\t\t\t} else {\n\n\t\t\t\tbody = [ ];\n\n\t\t\t}\n\n\t\t\tconst tokens = new ArrayStream( body );\n\n\t\t\tloop: for ( let i = 0; i < header.elements.length; i ++ ) {\n\n\t\t\t\tconst elementDesc = header.elements[ i ];\n\t\t\t\tconst attributeMap = mapElementAttributes( elementDesc.properties );\n\n\t\t\t\tfor ( let j = 0; j < elementDesc.count; j ++ ) {\n\n\t\t\t\t\tconst element = parseASCIIElement( elementDesc.properties, tokens );\n\n\t\t\t\t\tif ( ! element ) break loop;\n\n\t\t\t\t\thandleElement( buffer, elementDesc.name, element, attributeMap );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn postProcess( buffer );\n\n\t\t}\n\n\t\tfunction postProcess( buffer ) {\n\n\t\t\tlet geometry = new BufferGeometry();\n\n\t\t\t// mandatory buffer data\n\n\t\t\tif ( buffer.indices.length > 0 ) {\n\n\t\t\t\tgeometry.setIndex( buffer.indices );\n\n\t\t\t}\n\n\t\t\tgeometry.setAttribute( 'position', new Float32BufferAttribute( buffer.vertices, 3 ) );\n\n\t\t\t// optional buffer data\n\n\t\t\tif ( buffer.normals.length > 0 ) {\n\n\t\t\t\tgeometry.setAttribute( 'normal', new Float32BufferAttribute( buffer.normals, 3 ) );\n\n\t\t\t}\n\n\t\t\tif ( buffer.uvs.length > 0 ) {\n\n\t\t\t\tgeometry.setAttribute( 'uv', new Float32BufferAttribute( buffer.uvs, 2 ) );\n\n\t\t\t}\n\n\t\t\tif ( buffer.colors.length > 0 ) {\n\n\t\t\t\tgeometry.setAttribute( 'color', new Float32BufferAttribute( buffer.colors, 3 ) );\n\n\t\t\t}\n\n\t\t\tif ( buffer.faceVertexUvs.length > 0 || buffer.faceVertexColors.length > 0 ) {\n\n\t\t\t\tgeometry = geometry.toNonIndexed();\n\n\t\t\t\tif ( buffer.faceVertexUvs.length > 0 ) geometry.setAttribute( 'uv', new Float32BufferAttribute( buffer.faceVertexUvs, 2 ) );\n\t\t\t\tif ( buffer.faceVertexColors.length > 0 ) geometry.setAttribute( 'color', new Float32BufferAttribute( buffer.faceVertexColors, 3 ) );\n\n\t\t\t}\n\n\t\t\t// custom buffer data\n\n\t\t\tfor ( const customProperty of Object.keys( scope.customPropertyMapping ) ) {\n\n\t\t\t\tif ( buffer[ customProperty ].length > 0 ) {\n\n\t\t\t\t  \tgeometry.setAttribute(\n\t\t\t\t\t\tcustomProperty,\n\t\t\t\t\t\tnew Float32BufferAttribute(\n\t\t\t\t\t  \t\tbuffer[ customProperty ],\n\t\t\t\t\t  \t\tscope.customPropertyMapping[ customProperty ].length\n\t\t\t\t\t\t)\n\t\t\t\t  \t);\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tgeometry.computeBoundingSphere();\n\n\t\t\treturn geometry;\n\n\t\t}\n\n\t\tfunction handleElement( buffer, elementName, element, cacheEntry ) {\n\n\t\t\tif ( elementName === 'vertex' ) {\n\n\t\t\t\tbuffer.vertices.push( element[ cacheEntry.attrX ], element[ cacheEntry.attrY ], element[ cacheEntry.attrZ ] );\n\n\t\t\t\tif ( cacheEntry.attrNX !== null && cacheEntry.attrNY !== null && cacheEntry.attrNZ !== null ) {\n\n\t\t\t\t\tbuffer.normals.push( element[ cacheEntry.attrNX ], element[ cacheEntry.attrNY ], element[ cacheEntry.attrNZ ] );\n\n\t\t\t\t}\n\n\t\t\t\tif ( cacheEntry.attrS !== null && cacheEntry.attrT !== null ) {\n\n\t\t\t\t\tbuffer.uvs.push( element[ cacheEntry.attrS ], element[ cacheEntry.attrT ] );\n\n\t\t\t\t}\n\n\t\t\t\tif ( cacheEntry.attrR !== null && cacheEntry.attrG !== null && cacheEntry.attrB !== null ) {\n\n\t\t\t\t\t_color.setRGB(\n\t\t\t\t\t\telement[ cacheEntry.attrR ] / 255.0,\n\t\t\t\t\t\telement[ cacheEntry.attrG ] / 255.0,\n\t\t\t\t\t\telement[ cacheEntry.attrB ] / 255.0\n\t\t\t\t\t).convertSRGBToLinear();\n\n\t\t\t\t\tbuffer.colors.push( _color.r, _color.g, _color.b );\n\n\t\t\t\t}\n\n\t\t\t\tfor ( const customProperty of Object.keys( scope.customPropertyMapping ) ) {\n\n\t\t\t\t\tfor ( const elementProperty of scope.customPropertyMapping[ customProperty ] ) {\n\n\t\t\t\t\t  buffer[ customProperty ].push( element[ elementProperty ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} else if ( elementName === 'face' ) {\n\n\t\t\t\tconst vertex_indices = element.vertex_indices || element.vertex_index; // issue #9338\n\t\t\t\tconst texcoord = element.texcoord;\n\n\t\t\t\tif ( vertex_indices.length === 3 ) {\n\n\t\t\t\t\tbuffer.indices.push( vertex_indices[ 0 ], vertex_indices[ 1 ], vertex_indices[ 2 ] );\n\n\t\t\t\t\tif ( texcoord && texcoord.length === 6 ) {\n\n\t\t\t\t\t\tbuffer.faceVertexUvs.push( texcoord[ 0 ], texcoord[ 1 ] );\n\t\t\t\t\t\tbuffer.faceVertexUvs.push( texcoord[ 2 ], texcoord[ 3 ] );\n\t\t\t\t\t\tbuffer.faceVertexUvs.push( texcoord[ 4 ], texcoord[ 5 ] );\n\n\t\t\t\t\t}\n\n\t\t\t\t} else if ( vertex_indices.length === 4 ) {\n\n\t\t\t\t\tbuffer.indices.push( vertex_indices[ 0 ], vertex_indices[ 1 ], vertex_indices[ 3 ] );\n\t\t\t\t\tbuffer.indices.push( vertex_indices[ 1 ], vertex_indices[ 2 ], vertex_indices[ 3 ] );\n\n\t\t\t\t}\n\n\t\t\t\t// face colors\n\n\t\t\t\tif ( cacheEntry.attrR !== null && cacheEntry.attrG !== null && cacheEntry.attrB !== null ) {\n\n\t\t\t\t\t_color.setRGB(\n\t\t\t\t\t\telement[ cacheEntry.attrR ] / 255.0,\n\t\t\t\t\t\telement[ cacheEntry.attrG ] / 255.0,\n\t\t\t\t\t\telement[ cacheEntry.attrB ] / 255.0\n\t\t\t\t\t).convertSRGBToLinear();\n\t\t\t\t\tbuffer.faceVertexColors.push( _color.r, _color.g, _color.b );\n\t\t\t\t\tbuffer.faceVertexColors.push( _color.r, _color.g, _color.b );\n\t\t\t\t\tbuffer.faceVertexColors.push( _color.r, _color.g, _color.b );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction binaryReadElement( at, properties ) {\n\n\t\t\tconst element = {};\n\t\t\tlet read = 0;\n\n\t\t\tfor ( let i = 0; i < properties.length; i ++ ) {\n\n\t\t\t\tconst property = properties[ i ];\n\t\t\t\tconst valueReader = property.valueReader;\n\n\t\t\t\tif ( property.type === 'list' ) {\n\n\t\t\t\t\tconst list = [];\n\n\t\t\t\t\tconst n = property.countReader.read( at + read );\n\t\t\t\t\tread += property.countReader.size;\n\n\t\t\t\t\tfor ( let j = 0; j < n; j ++ ) {\n\n\t\t\t\t\t\tlist.push( valueReader.read( at + read ) );\n\t\t\t\t\t\tread += valueReader.size;\n\n\t\t\t\t\t}\n\n\t\t\t\t\telement[ property.name ] = list;\n\n\t\t\t\t} else {\n\n\t\t\t\t\telement[ property.name ] = valueReader.read( at + read );\n\t\t\t\t\tread += valueReader.size;\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn [ element, read ];\n\n\t\t}\n\n\t\tfunction setPropertyBinaryReaders( properties, body, little_endian ) {\n\n\t\t\tfunction getBinaryReader( dataview, type, little_endian ) {\n\n\t\t\t\tswitch ( type ) {\n\n\t\t\t\t\t// corespondences for non-specific length types here match rply:\n\t\t\t\t\tcase 'int8':\tcase 'char':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getInt8( at );\n\n\t\t\t\t\t}, size: 1 };\n\t\t\t\t\tcase 'uint8':\tcase 'uchar':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getUint8( at );\n\n\t\t\t\t\t}, size: 1 };\n\t\t\t\t\tcase 'int16':\tcase 'short':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getInt16( at, little_endian );\n\n\t\t\t\t\t}, size: 2 };\n\t\t\t\t\tcase 'uint16':\tcase 'ushort':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getUint16( at, little_endian );\n\n\t\t\t\t\t}, size: 2 };\n\t\t\t\t\tcase 'int32':\tcase 'int':\t\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getInt32( at, little_endian );\n\n\t\t\t\t\t}, size: 4 };\n\t\t\t\t\tcase 'uint32':\tcase 'uint':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getUint32( at, little_endian );\n\n\t\t\t\t\t}, size: 4 };\n\t\t\t\t\tcase 'float32': case 'float':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getFloat32( at, little_endian );\n\n\t\t\t\t\t}, size: 4 };\n\t\t\t\t\tcase 'float64': case 'double':\treturn { read: ( at ) => {\n\n\t\t\t\t\t\treturn dataview.getFloat64( at, little_endian );\n\n\t\t\t\t\t}, size: 8 };\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, l = properties.length; i < l; i ++ ) {\n\n\t\t\t\tconst property = properties[ i ];\n\n\t\t\t\tif ( property.type === 'list' ) {\n\n\t\t\t\t\tproperty.countReader = getBinaryReader( body, property.countType, little_endian );\n\t\t\t\t\tproperty.valueReader = getBinaryReader( body, property.itemType, little_endian );\n\n\t\t\t\t} else {\n\n\t\t\t\t\tproperty.valueReader = getBinaryReader( body, property.type, little_endian );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction parseBinary( data, header ) {\n\n\t\t\tconst buffer = createBuffer();\n\n\t\t\tconst little_endian = ( header.format === 'binary_little_endian' );\n\t\t\tconst body = new DataView( data, header.headerLength );\n\t\t\tlet result, loc = 0;\n\n\t\t\tfor ( let currentElement = 0; currentElement < header.elements.length; currentElement ++ ) {\n\n\t\t\t\tconst elementDesc = header.elements[ currentElement ];\n\t\t\t\tconst properties = elementDesc.properties;\n\t\t\t\tconst attributeMap = mapElementAttributes( properties );\n\n\t\t\t\tsetPropertyBinaryReaders( properties, body, little_endian );\n\n\t\t\t\tfor ( let currentElementCount = 0; currentElementCount < elementDesc.count; currentElementCount ++ ) {\n\n\t\t\t\t\tresult = binaryReadElement( loc, properties );\n\t\t\t\t\tloc += result[ 1 ];\n\t\t\t\t\tconst element = result[ 0 ];\n\n\t\t\t\t\thandleElement( buffer, elementDesc.name, element, attributeMap );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\treturn postProcess( buffer );\n\n\t\t}\n\n\t\tfunction extractHeaderText( bytes ) {\n\n\t\t\tlet i = 0;\n\t\t\tlet cont = true;\n\n\t\t\tlet line = '';\n\t\t\tconst lines = [];\n\n\t\t\tconst startLine = new TextDecoder().decode( bytes.subarray( 0, 5 ) );\n\t\t\tconst hasCRNL = /^ply\\r\\n/.test( startLine );\n\n\t\t\tdo {\n\n\t\t\t\tconst c = String.fromCharCode( bytes[ i ++ ] );\n\n\t\t\t\tif ( c !== '\\n' && c !== '\\r' ) {\n\n\t\t\t\t\tline += c;\n\n\t\t\t\t} else {\n\n\t\t\t\t\tif ( line === 'end_header' ) cont = false;\n\t\t\t\t\tif ( line !== '' ) {\n\n\t\t\t\t\t\tlines.push( line );\n\t\t\t\t\t\tline = '';\n\n\t\t\t\t\t}\n\n\t\t\t\t}\n\n\t\t\t} while ( cont && i < bytes.length );\n\n\t\t\t// ascii section using \\r\\n as line endings\n\t\t\tif ( hasCRNL === true ) i ++;\n\n\t\t\treturn { headerText: lines.join( '\\r' ) + '\\r', headerLength: i };\n\n\t\t}\n\n\t\t//\n\n\t\tlet geometry;\n\t\tconst scope = this;\n\n\t\tif ( data instanceof ArrayBuffer ) {\n\n\t\t\tconst bytes = new Uint8Array( data );\n\t\t\tconst { headerText, headerLength } = extractHeaderText( bytes );\n\t\t\tconst header = parseHeader( headerText, headerLength );\n\n\t\t\tif ( header.format === 'ascii' ) {\n\n\t\t\t\tconst text = new TextDecoder().decode( bytes );\n\n\t\t\t\tgeometry = parseASCII( text, header );\n\n\t\t\t} else {\n\n\t\t\t\tgeometry = parseBinary( data, header );\n\n\t\t\t}\n\n\t\t} else {\n\n\t\t\tgeometry = parseASCII( data, parseHeader( data ) );\n\n\t\t}\n\n\t\treturn geometry;\n\n\t}\n\n}\n\nclass ArrayStream {\n\n\tconstructor( arr ) {\n\n\t\tthis.arr = arr;\n\t\tthis.i = 0;\n\n\t}\n\n\tempty() {\n\n\t\treturn this.i >= this.arr.length;\n\n\t}\n\n\tnext() {\n\n\t\treturn this.arr[ this.i ++ ];\n\n\t}\n\n}\n\nexport { PLYLoader };\n"], "mappings": ";;;;;;;;;;AA4CA,IAAM,SAAS,IAAI,MAAM;AAEzB,IAAM,YAAN,cAAwB,OAAO;AAAA,EAE9B,YAAa,SAAU;AAEtB,UAAO,OAAQ;AAEf,SAAK,sBAAsB,CAAC;AAC5B,SAAK,wBAAwB,CAAC;AAAA,EAE/B;AAAA,EAEA,KAAM,KAAK,QAAQ,YAAY,SAAU;AAExC,UAAM,QAAQ;AAEd,UAAM,SAAS,IAAI,WAAY,KAAK,OAAQ;AAC5C,WAAO,QAAS,KAAK,IAAK;AAC1B,WAAO,gBAAiB,aAAc;AACtC,WAAO,iBAAkB,KAAK,aAAc;AAC5C,WAAO,mBAAoB,KAAK,eAAgB;AAChD,WAAO,KAAM,KAAK,SAAW,MAAO;AAEnC,UAAI;AAEH,eAAQ,MAAM,MAAO,IAAK,CAAE;AAAA,MAE7B,SAAU,GAAI;AAEb,YAAK,SAAU;AAEd,kBAAS,CAAE;AAAA,QAEZ,OAAO;AAEN,kBAAQ,MAAO,CAAE;AAAA,QAElB;AAEA,cAAM,QAAQ,UAAW,GAAI;AAAA,MAE9B;AAAA,IAED,GAAG,YAAY,OAAQ;AAAA,EAExB;AAAA,EAEA,uBAAwB,SAAU;AAEjC,SAAK,sBAAsB;AAAA,EAE5B;AAAA,EAEA,6BAA8B,SAAU;AAEvC,SAAK,wBAAwB;AAAA,EAE9B;AAAA,EAEA,MAAO,MAAO;AAEb,aAAS,YAAaA,OAAM,eAAe,GAAI;AAE9C,YAAM,gBAAgB;AACtB,UAAI,aAAa;AACjB,YAAM,SAAS,cAAc,KAAMA,KAAK;AAExC,UAAK,WAAW,MAAO;AAEtB,qBAAa,OAAQ,CAAE;AAAA,MAExB;AAEA,YAAM,SAAS;AAAA,QACd,UAAU,CAAC;AAAA,QACX,UAAU,CAAC;AAAA,QACX;AAAA,QACA,SAAS;AAAA,MACV;AAEA,YAAM,QAAQ,WAAW,MAAO,YAAa;AAC7C,UAAI;AAEJ,eAAS,0BAA2B,eAAe,qBAAsB;AAExE,cAAM,WAAW,EAAE,MAAM,cAAe,CAAE,EAAE;AAE5C,YAAK,SAAS,SAAS,QAAS;AAE/B,mBAAS,OAAO,cAAe,CAAE;AACjC,mBAAS,YAAY,cAAe,CAAE;AACtC,mBAAS,WAAW,cAAe,CAAE;AAAA,QAEtC,OAAO;AAEN,mBAAS,OAAO,cAAe,CAAE;AAAA,QAElC;AAEA,YAAK,SAAS,QAAQ,qBAAsB;AAE3C,mBAAS,OAAO,oBAAqB,SAAS,IAAK;AAAA,QAEpD;AAEA,eAAO;AAAA,MAER;AAEA,eAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAO;AAEzC,YAAI,OAAO,MAAO,CAAE;AACpB,eAAO,KAAK,KAAK;AAEjB,YAAK,SAAS,GAAK;AAEnB,cAAM,aAAa,KAAK,MAAO,KAAM;AACrC,cAAM,WAAW,WAAW,MAAM;AAClC,eAAO,WAAW,KAAM,GAAI;AAE5B,gBAAS,UAAW;AAAA,UAEnB,KAAK;AAEJ,mBAAO,SAAS,WAAY,CAAE;AAC9B,mBAAO,UAAU,WAAY,CAAE;AAE/B;AAAA,UAED,KAAK;AAEJ,mBAAO,SAAS,KAAM,IAAK;AAE3B;AAAA,UAED,KAAK;AAEJ,gBAAK,mBAAmB,QAAY;AAEnC,qBAAO,SAAS,KAAM,cAAe;AAAA,YAEtC;AAEA,6BAAiB,CAAC;AAClB,2BAAe,OAAO,WAAY,CAAE;AACpC,2BAAe,QAAQ,SAAU,WAAY,CAAE,CAAE;AACjD,2BAAe,aAAa,CAAC;AAE7B;AAAA,UAED,KAAK;AAEJ,2BAAe,WAAW,KAAM,0BAA2B,YAAY,MAAM,mBAAoB,CAAE;AAEnG;AAAA,UAED,KAAK;AAEJ,mBAAO,UAAU;AAEjB;AAAA,UAGD;AAEC,oBAAQ,IAAK,aAAa,UAAU,UAAW;AAAA,QAEjD;AAAA,MAED;AAEA,UAAK,mBAAmB,QAAY;AAEnC,eAAO,SAAS,KAAM,cAAe;AAAA,MAEtC;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,iBAAkB,GAAG,MAAO;AAEpC,cAAS,MAAO;AAAA,QAEf,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAS,KAAK;AAAA,QAAS,KAAK;AAAA,QAAU,KAAK;AAAA,QAAO,KAAK;AAAA,QACzE,KAAK;AAAA,QAAQ,KAAK;AAAA,QAAS,KAAK;AAAA,QAAS,KAAK;AAAA,QAAU,KAAK;AAAA,QAAS,KAAK;AAE1E,iBAAO,SAAU,CAAE;AAAA,QAEpB,KAAK;AAAA,QAAS,KAAK;AAAA,QAAU,KAAK;AAAA,QAAW,KAAK;AAEjD,iBAAO,WAAY,CAAE;AAAA,MAEvB;AAAA,IAED;AAEA,aAAS,kBAAmB,YAAY,QAAS;AAEhD,YAAM,UAAU,CAAC;AAEjB,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,YAAK,OAAO,MAAM,EAAI,QAAO;AAE7B,YAAK,WAAY,CAAE,EAAE,SAAS,QAAS;AAEtC,gBAAM,OAAO,CAAC;AACd,gBAAM,IAAI,iBAAkB,OAAO,KAAK,GAAG,WAAY,CAAE,EAAE,SAAU;AAErE,mBAAU,IAAI,GAAG,IAAI,GAAG,KAAO;AAE9B,gBAAK,OAAO,MAAM,EAAI,QAAO;AAE7B,iBAAK,KAAM,iBAAkB,OAAO,KAAK,GAAG,WAAY,CAAE,EAAE,QAAS,CAAE;AAAA,UAExE;AAEA,kBAAS,WAAY,CAAE,EAAE,IAAK,IAAI;AAAA,QAEnC,OAAO;AAEN,kBAAS,WAAY,CAAE,EAAE,IAAK,IAAI,iBAAkB,OAAO,KAAK,GAAG,WAAY,CAAE,EAAE,IAAK;AAAA,QAEzF;AAAA,MAED;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,eAAe;AAEvB,YAAM,SAAS;AAAA,QACb,SAAS,CAAC;AAAA,QACV,UAAU,CAAC;AAAA,QACX,SAAS,CAAC;AAAA,QACV,KAAK,CAAC;AAAA,QACN,eAAe,CAAC;AAAA,QAChB,QAAQ,CAAC;AAAA,QACT,kBAAkB,CAAC;AAAA,MACrB;AAEA,iBAAY,kBAAkB,OAAO,KAAM,MAAM,qBAAsB,GAAI;AAEzE,eAAQ,cAAe,IAAI,CAAC;AAAA,MAE9B;AAEA,aAAO;AAAA,IAER;AAEA,aAAS,qBAAsB,YAAa;AAE3C,YAAM,eAAe,WAAW,IAAK,cAAY;AAEhD,eAAO,SAAS;AAAA,MAEjB,CAAE;AAEF,eAAS,aAAc,OAAQ;AAE9B,iBAAU,IAAI,GAAG,IAAI,MAAM,QAAQ,IAAI,GAAG,KAAO;AAEhD,gBAAM,OAAO,MAAO,CAAE;AAEtB,cAAK,aAAa,SAAU,IAAK,EAAI,QAAO;AAAA,QAE7C;AAEA,eAAO;AAAA,MAER;AAEA,aAAO;AAAA,QACN,OAAO,aAAc,CAAE,KAAK,MAAM,MAAO,CAAE,KAAK;AAAA,QAChD,OAAO,aAAc,CAAE,KAAK,MAAM,MAAO,CAAE,KAAK;AAAA,QAChD,OAAO,aAAc,CAAE,KAAK,MAAM,MAAO,CAAE,KAAK;AAAA,QAChD,QAAQ,aAAc,CAAE,MAAM,SAAU,CAAE;AAAA,QAC1C,QAAQ,aAAc,CAAE,MAAM,SAAU,CAAE;AAAA,QAC1C,QAAQ,aAAc,CAAE,MAAM,SAAU,CAAE;AAAA,QAC1C,OAAO,aAAc,CAAE,KAAK,KAAK,aAAa,IAAK,CAAE;AAAA,QACrD,OAAO,aAAc,CAAE,KAAK,KAAK,aAAa,IAAK,CAAE;AAAA,QACrD,OAAO,aAAc,CAAE,OAAO,eAAe,KAAK,WAAY,CAAE;AAAA,QAChE,OAAO,aAAc,CAAE,SAAS,iBAAiB,KAAK,WAAY,CAAE;AAAA,QACpE,OAAO,aAAc,CAAE,QAAQ,gBAAgB,KAAK,WAAY,CAAE;AAAA,MACnE;AAAA,IAED;AAEA,aAAS,WAAYA,OAAM,QAAS;AAInC,YAAM,SAAS,aAAa;AAE5B,YAAM,cAAc;AACpB,UAAI,MAAM;AAEV,WAAO,UAAU,YAAY,KAAMA,KAAK,OAAQ,MAAO;AAEtD,eAAO,QAAS,CAAE,EAAE,MAAO,KAAM;AAAA,MAElC,OAAO;AAEN,eAAO,CAAE;AAAA,MAEV;AAEA,YAAM,SAAS,IAAI,YAAa,IAAK;AAErC,WAAM,UAAU,IAAI,GAAG,IAAI,OAAO,SAAS,QAAQ,KAAO;AAEzD,cAAM,cAAc,OAAO,SAAU,CAAE;AACvC,cAAM,eAAe,qBAAsB,YAAY,UAAW;AAElE,iBAAU,IAAI,GAAG,IAAI,YAAY,OAAO,KAAO;AAE9C,gBAAM,UAAU,kBAAmB,YAAY,YAAY,MAAO;AAElE,cAAK,CAAE,QAAU,OAAM;AAEvB,wBAAe,QAAQ,YAAY,MAAM,SAAS,YAAa;AAAA,QAEhE;AAAA,MAED;AAEA,aAAO,YAAa,MAAO;AAAA,IAE5B;AAEA,aAAS,YAAa,QAAS;AAE9B,UAAIC,YAAW,IAAI,eAAe;AAIlC,UAAK,OAAO,QAAQ,SAAS,GAAI;AAEhC,QAAAA,UAAS,SAAU,OAAO,OAAQ;AAAA,MAEnC;AAEA,MAAAA,UAAS,aAAc,YAAY,IAAI,uBAAwB,OAAO,UAAU,CAAE,CAAE;AAIpF,UAAK,OAAO,QAAQ,SAAS,GAAI;AAEhC,QAAAA,UAAS,aAAc,UAAU,IAAI,uBAAwB,OAAO,SAAS,CAAE,CAAE;AAAA,MAElF;AAEA,UAAK,OAAO,IAAI,SAAS,GAAI;AAE5B,QAAAA,UAAS,aAAc,MAAM,IAAI,uBAAwB,OAAO,KAAK,CAAE,CAAE;AAAA,MAE1E;AAEA,UAAK,OAAO,OAAO,SAAS,GAAI;AAE/B,QAAAA,UAAS,aAAc,SAAS,IAAI,uBAAwB,OAAO,QAAQ,CAAE,CAAE;AAAA,MAEhF;AAEA,UAAK,OAAO,cAAc,SAAS,KAAK,OAAO,iBAAiB,SAAS,GAAI;AAE5E,QAAAA,YAAWA,UAAS,aAAa;AAEjC,YAAK,OAAO,cAAc,SAAS,EAAI,CAAAA,UAAS,aAAc,MAAM,IAAI,uBAAwB,OAAO,eAAe,CAAE,CAAE;AAC1H,YAAK,OAAO,iBAAiB,SAAS,EAAI,CAAAA,UAAS,aAAc,SAAS,IAAI,uBAAwB,OAAO,kBAAkB,CAAE,CAAE;AAAA,MAEpI;AAIA,iBAAY,kBAAkB,OAAO,KAAM,MAAM,qBAAsB,GAAI;AAE1E,YAAK,OAAQ,cAAe,EAAE,SAAS,GAAI;AAExC,UAAAA,UAAS;AAAA,YACV;AAAA,YACA,IAAI;AAAA,cACD,OAAQ,cAAe;AAAA,cACvB,MAAM,sBAAuB,cAAe,EAAE;AAAA,YACjD;AAAA,UACC;AAAA,QAEH;AAAA,MAED;AAEA,MAAAA,UAAS,sBAAsB;AAE/B,aAAOA;AAAA,IAER;AAEA,aAAS,cAAe,QAAQ,aAAa,SAAS,YAAa;AAElE,UAAK,gBAAgB,UAAW;AAE/B,eAAO,SAAS,KAAM,QAAS,WAAW,KAAM,GAAG,QAAS,WAAW,KAAM,GAAG,QAAS,WAAW,KAAM,CAAE;AAE5G,YAAK,WAAW,WAAW,QAAQ,WAAW,WAAW,QAAQ,WAAW,WAAW,MAAO;AAE7F,iBAAO,QAAQ,KAAM,QAAS,WAAW,MAAO,GAAG,QAAS,WAAW,MAAO,GAAG,QAAS,WAAW,MAAO,CAAE;AAAA,QAE/G;AAEA,YAAK,WAAW,UAAU,QAAQ,WAAW,UAAU,MAAO;AAE7D,iBAAO,IAAI,KAAM,QAAS,WAAW,KAAM,GAAG,QAAS,WAAW,KAAM,CAAE;AAAA,QAE3E;AAEA,YAAK,WAAW,UAAU,QAAQ,WAAW,UAAU,QAAQ,WAAW,UAAU,MAAO;AAE1F,iBAAO;AAAA,YACN,QAAS,WAAW,KAAM,IAAI;AAAA,YAC9B,QAAS,WAAW,KAAM,IAAI;AAAA,YAC9B,QAAS,WAAW,KAAM,IAAI;AAAA,UAC/B,EAAE,oBAAoB;AAEtB,iBAAO,OAAO,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAAA,QAElD;AAEA,mBAAY,kBAAkB,OAAO,KAAM,MAAM,qBAAsB,GAAI;AAE1E,qBAAY,mBAAmB,MAAM,sBAAuB,cAAe,GAAI;AAE7E,mBAAQ,cAAe,EAAE,KAAM,QAAS,eAAgB,CAAE;AAAA,UAE5D;AAAA,QAED;AAAA,MAED,WAAY,gBAAgB,QAAS;AAEpC,cAAM,iBAAiB,QAAQ,kBAAkB,QAAQ;AACzD,cAAM,WAAW,QAAQ;AAEzB,YAAK,eAAe,WAAW,GAAI;AAElC,iBAAO,QAAQ,KAAM,eAAgB,CAAE,GAAG,eAAgB,CAAE,GAAG,eAAgB,CAAE,CAAE;AAEnF,cAAK,YAAY,SAAS,WAAW,GAAI;AAExC,mBAAO,cAAc,KAAM,SAAU,CAAE,GAAG,SAAU,CAAE,CAAE;AACxD,mBAAO,cAAc,KAAM,SAAU,CAAE,GAAG,SAAU,CAAE,CAAE;AACxD,mBAAO,cAAc,KAAM,SAAU,CAAE,GAAG,SAAU,CAAE,CAAE;AAAA,UAEzD;AAAA,QAED,WAAY,eAAe,WAAW,GAAI;AAEzC,iBAAO,QAAQ,KAAM,eAAgB,CAAE,GAAG,eAAgB,CAAE,GAAG,eAAgB,CAAE,CAAE;AACnF,iBAAO,QAAQ,KAAM,eAAgB,CAAE,GAAG,eAAgB,CAAE,GAAG,eAAgB,CAAE,CAAE;AAAA,QAEpF;AAIA,YAAK,WAAW,UAAU,QAAQ,WAAW,UAAU,QAAQ,WAAW,UAAU,MAAO;AAE1F,iBAAO;AAAA,YACN,QAAS,WAAW,KAAM,IAAI;AAAA,YAC9B,QAAS,WAAW,KAAM,IAAI;AAAA,YAC9B,QAAS,WAAW,KAAM,IAAI;AAAA,UAC/B,EAAE,oBAAoB;AACtB,iBAAO,iBAAiB,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAC3D,iBAAO,iBAAiB,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAC3D,iBAAO,iBAAiB,KAAM,OAAO,GAAG,OAAO,GAAG,OAAO,CAAE;AAAA,QAE5D;AAAA,MAED;AAAA,IAED;AAEA,aAAS,kBAAmB,IAAI,YAAa;AAE5C,YAAM,UAAU,CAAC;AACjB,UAAI,OAAO;AAEX,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAO;AAE9C,cAAM,WAAW,WAAY,CAAE;AAC/B,cAAM,cAAc,SAAS;AAE7B,YAAK,SAAS,SAAS,QAAS;AAE/B,gBAAM,OAAO,CAAC;AAEd,gBAAM,IAAI,SAAS,YAAY,KAAM,KAAK,IAAK;AAC/C,kBAAQ,SAAS,YAAY;AAE7B,mBAAU,IAAI,GAAG,IAAI,GAAG,KAAO;AAE9B,iBAAK,KAAM,YAAY,KAAM,KAAK,IAAK,CAAE;AACzC,oBAAQ,YAAY;AAAA,UAErB;AAEA,kBAAS,SAAS,IAAK,IAAI;AAAA,QAE5B,OAAO;AAEN,kBAAS,SAAS,IAAK,IAAI,YAAY,KAAM,KAAK,IAAK;AACvD,kBAAQ,YAAY;AAAA,QAErB;AAAA,MAED;AAEA,aAAO,CAAE,SAAS,IAAK;AAAA,IAExB;AAEA,aAAS,yBAA0B,YAAY,MAAM,eAAgB;AAEpE,eAAS,gBAAiB,UAAU,MAAMC,gBAAgB;AAEzD,gBAAS,MAAO;AAAA,UAGf,KAAK;AAAA,UAAQ,KAAK;AAAQ,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAElD,qBAAO,SAAS,QAAS,EAAG;AAAA,YAE7B,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAS,KAAK;AAAS,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEpD,qBAAO,SAAS,SAAU,EAAG;AAAA,YAE9B,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAS,KAAK;AAAS,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEpD,qBAAO,SAAS,SAAU,IAAIA,cAAc;AAAA,YAE7C,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAU,KAAK;AAAU,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEtD,qBAAO,SAAS,UAAW,IAAIA,cAAc;AAAA,YAE9C,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAS,KAAK;AAAQ,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEnD,qBAAO,SAAS,SAAU,IAAIA,cAAc;AAAA,YAE7C,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAU,KAAK;AAAQ,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEpD,qBAAO,SAAS,UAAW,IAAIA,cAAc;AAAA,YAE9C,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAW,KAAK;AAAS,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEtD,qBAAO,SAAS,WAAY,IAAIA,cAAc;AAAA,YAE/C,GAAG,MAAM,EAAE;AAAA,UACX,KAAK;AAAA,UAAW,KAAK;AAAU,mBAAO,EAAE,MAAM,CAAE,OAAQ;AAEvD,qBAAO,SAAS,WAAY,IAAIA,cAAc;AAAA,YAE/C,GAAG,MAAM,EAAE;AAAA,QAEZ;AAAA,MAED;AAEA,eAAU,IAAI,GAAG,IAAI,WAAW,QAAQ,IAAI,GAAG,KAAO;AAErD,cAAM,WAAW,WAAY,CAAE;AAE/B,YAAK,SAAS,SAAS,QAAS;AAE/B,mBAAS,cAAc,gBAAiB,MAAM,SAAS,WAAW,aAAc;AAChF,mBAAS,cAAc,gBAAiB,MAAM,SAAS,UAAU,aAAc;AAAA,QAEhF,OAAO;AAEN,mBAAS,cAAc,gBAAiB,MAAM,SAAS,MAAM,aAAc;AAAA,QAE5E;AAAA,MAED;AAAA,IAED;AAEA,aAAS,YAAaF,OAAM,QAAS;AAEpC,YAAM,SAAS,aAAa;AAE5B,YAAM,gBAAkB,OAAO,WAAW;AAC1C,YAAM,OAAO,IAAI,SAAUA,OAAM,OAAO,YAAa;AACrD,UAAI,QAAQ,MAAM;AAElB,eAAU,iBAAiB,GAAG,iBAAiB,OAAO,SAAS,QAAQ,kBAAoB;AAE1F,cAAM,cAAc,OAAO,SAAU,cAAe;AACpD,cAAM,aAAa,YAAY;AAC/B,cAAM,eAAe,qBAAsB,UAAW;AAEtD,iCAA0B,YAAY,MAAM,aAAc;AAE1D,iBAAU,sBAAsB,GAAG,sBAAsB,YAAY,OAAO,uBAAyB;AAEpG,mBAAS,kBAAmB,KAAK,UAAW;AAC5C,iBAAO,OAAQ,CAAE;AACjB,gBAAM,UAAU,OAAQ,CAAE;AAE1B,wBAAe,QAAQ,YAAY,MAAM,SAAS,YAAa;AAAA,QAEhE;AAAA,MAED;AAEA,aAAO,YAAa,MAAO;AAAA,IAE5B;AAEA,aAAS,kBAAmB,OAAQ;AAEnC,UAAI,IAAI;AACR,UAAI,OAAO;AAEX,UAAI,OAAO;AACX,YAAM,QAAQ,CAAC;AAEf,YAAM,YAAY,IAAI,YAAY,EAAE,OAAQ,MAAM,SAAU,GAAG,CAAE,CAAE;AACnE,YAAM,UAAU,WAAW,KAAM,SAAU;AAE3C,SAAG;AAEF,cAAM,IAAI,OAAO,aAAc,MAAO,GAAK,CAAE;AAE7C,YAAK,MAAM,QAAQ,MAAM,MAAO;AAE/B,kBAAQ;AAAA,QAET,OAAO;AAEN,cAAK,SAAS,aAAe,QAAO;AACpC,cAAK,SAAS,IAAK;AAElB,kBAAM,KAAM,IAAK;AACjB,mBAAO;AAAA,UAER;AAAA,QAED;AAAA,MAED,SAAU,QAAQ,IAAI,MAAM;AAG5B,UAAK,YAAY,KAAO;AAExB,aAAO,EAAE,YAAY,MAAM,KAAM,IAAK,IAAI,MAAM,cAAc,EAAE;AAAA,IAEjE;AAIA,QAAI;AACJ,UAAM,QAAQ;AAEd,QAAK,gBAAgB,aAAc;AAElC,YAAM,QAAQ,IAAI,WAAY,IAAK;AACnC,YAAM,EAAE,YAAY,aAAa,IAAI,kBAAmB,KAAM;AAC9D,YAAM,SAAS,YAAa,YAAY,YAAa;AAErD,UAAK,OAAO,WAAW,SAAU;AAEhC,cAAM,OAAO,IAAI,YAAY,EAAE,OAAQ,KAAM;AAE7C,mBAAW,WAAY,MAAM,MAAO;AAAA,MAErC,OAAO;AAEN,mBAAW,YAAa,MAAM,MAAO;AAAA,MAEtC;AAAA,IAED,OAAO;AAEN,iBAAW,WAAY,MAAM,YAAa,IAAK,CAAE;AAAA,IAElD;AAEA,WAAO;AAAA,EAER;AAED;AAEA,IAAM,cAAN,MAAkB;AAAA,EAEjB,YAAa,KAAM;AAElB,SAAK,MAAM;AACX,SAAK,IAAI;AAAA,EAEV;AAAA,EAEA,QAAQ;AAEP,WAAO,KAAK,KAAK,KAAK,IAAI;AAAA,EAE3B;AAAA,EAEA,OAAO;AAEN,WAAO,KAAK,IAAK,KAAK,GAAK;AAAA,EAE5B;AAED;", "names": ["data", "geometry", "little_endian"]}