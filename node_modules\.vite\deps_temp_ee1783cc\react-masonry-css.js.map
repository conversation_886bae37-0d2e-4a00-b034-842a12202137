{"version": 3, "sources": ["../../react-masonry-css/dist/react-masonry-css.module.js"], "sourcesContent": ["import React from 'react';\n\nfunction _objectWithoutProperties(source, excluded) { if (source == null) return {}; var target = _objectWithoutPropertiesLoose(source, excluded); var key, i; if (Object.getOwnPropertySymbols) { var sourceSymbolKeys = Object.getOwnPropertySymbols(source); for (i = 0; i < sourceSymbolKeys.length; i++) { key = sourceSymbolKeys[i]; if (excluded.indexOf(key) >= 0) continue; if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue; target[key] = source[key]; } } return target; }\n\nfunction _objectWithoutPropertiesLoose(source, excluded) { if (source == null) return {}; var target = {}; var sourceKeys = Object.keys(source); var key, i; for (i = 0; i < sourceKeys.length; i++) { key = sourceKeys[i]; if (excluded.indexOf(key) >= 0) continue; target[key] = source[key]; } return target; }\n\nfunction _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nconst defaultProps = {\n  breakpointCols: undefined,\n  // optional, number or object { default: number, [key: number]: number }\n  className: undefined,\n  // required, string\n  columnClassName: undefined,\n  // optional, string\n  // Any React children. Typically an array of JSX items\n  children: undefined,\n  // Custom attributes, however it is advised against\n  // using these to prevent unintended issues and future conflicts\n  // ...any other attribute, will be added to the container\n  columnAttrs: undefined,\n  // object, added to the columns\n  // Deprecated props\n  // The column property is deprecated.\n  // It is an alias of the `columnAttrs` property\n  column: undefined\n};\nconst DEFAULT_COLUMNS = 2;\n\nclass Masonry extends React.Component {\n  constructor(props) {\n    super(props); // Correct scope for when methods are accessed externally\n\n    this.reCalculateColumnCount = this.reCalculateColumnCount.bind(this);\n    this.reCalculateColumnCountDebounce = this.reCalculateColumnCountDebounce.bind(this); // default state\n\n    let columnCount;\n\n    if (this.props.breakpointCols && this.props.breakpointCols.default) {\n      columnCount = this.props.breakpointCols.default;\n    } else {\n      columnCount = parseInt(this.props.breakpointCols) || DEFAULT_COLUMNS;\n    }\n\n    this.state = {\n      columnCount\n    };\n  }\n\n  componentDidMount() {\n    this.reCalculateColumnCount(); // window may not be available in some environments\n\n    if (window) {\n      window.addEventListener('resize', this.reCalculateColumnCountDebounce);\n    }\n  }\n\n  componentDidUpdate() {\n    this.reCalculateColumnCount();\n  }\n\n  componentWillUnmount() {\n    if (window) {\n      window.removeEventListener('resize', this.reCalculateColumnCountDebounce);\n    }\n  }\n\n  reCalculateColumnCountDebounce() {\n    if (!window || !window.requestAnimationFrame) {\n      // IE10+\n      this.reCalculateColumnCount();\n      return;\n    }\n\n    if (window.cancelAnimationFrame) {\n      // IE10+\n      window.cancelAnimationFrame(this._lastRecalculateAnimationFrame);\n    }\n\n    this._lastRecalculateAnimationFrame = window.requestAnimationFrame(() => {\n      this.reCalculateColumnCount();\n    });\n  }\n\n  reCalculateColumnCount() {\n    const windowWidth = window && window.innerWidth || Infinity;\n    let breakpointColsObject = this.props.breakpointCols; // Allow passing a single number to `breakpointCols` instead of an object\n\n    if (typeof breakpointColsObject !== 'object') {\n      breakpointColsObject = {\n        default: parseInt(breakpointColsObject) || DEFAULT_COLUMNS\n      };\n    }\n\n    let matchedBreakpoint = Infinity;\n    let columns = breakpointColsObject.default || DEFAULT_COLUMNS;\n\n    for (let breakpoint in breakpointColsObject) {\n      const optBreakpoint = parseInt(breakpoint);\n      const isCurrentBreakpoint = optBreakpoint > 0 && windowWidth <= optBreakpoint;\n\n      if (isCurrentBreakpoint && optBreakpoint < matchedBreakpoint) {\n        matchedBreakpoint = optBreakpoint;\n        columns = breakpointColsObject[breakpoint];\n      }\n    }\n\n    columns = Math.max(1, parseInt(columns) || 1);\n\n    if (this.state.columnCount !== columns) {\n      this.setState({\n        columnCount: columns\n      });\n    }\n  }\n\n  itemsInColumns() {\n    const currentColumnCount = this.state.columnCount;\n    const itemsInColumns = new Array(currentColumnCount); // Force children to be handled as an array\n\n    const items = React.Children.toArray(this.props.children);\n\n    for (let i = 0; i < items.length; i++) {\n      const columnIndex = i % currentColumnCount;\n\n      if (!itemsInColumns[columnIndex]) {\n        itemsInColumns[columnIndex] = [];\n      }\n\n      itemsInColumns[columnIndex].push(items[i]);\n    }\n\n    return itemsInColumns;\n  }\n\n  renderColumns() {\n    const {\n      column,\n      columnAttrs = {},\n      columnClassName\n    } = this.props;\n    const childrenInColumns = this.itemsInColumns();\n    const columnWidth = `${100 / childrenInColumns.length}%`;\n    let className = columnClassName;\n\n    if (className && typeof className !== 'string') {\n      this.logDeprecated('The property \"columnClassName\" requires a string'); // This is a deprecated default and will be removed soon.\n\n      if (typeof className === 'undefined') {\n        className = 'my-masonry-grid_column';\n      }\n    }\n\n    const columnAttributes = _objectSpread(_objectSpread(_objectSpread({}, column), columnAttrs), {}, {\n      style: _objectSpread(_objectSpread({}, columnAttrs.style), {}, {\n        width: columnWidth\n      }),\n      className\n    });\n\n    return childrenInColumns.map((items, i) => {\n      return /*#__PURE__*/React.createElement(\"div\", _extends({}, columnAttributes, {\n        key: i\n      }), items);\n    });\n  }\n\n  logDeprecated(message) {\n    console.error('[Masonry]', message);\n  }\n\n  render() {\n    const _this$props = this.props,\n          {\n      // ignored\n      children,\n      breakpointCols,\n      columnClassName,\n      columnAttrs,\n      column,\n      // used\n      className\n    } = _this$props,\n          rest = _objectWithoutProperties(_this$props, [\"children\", \"breakpointCols\", \"columnClassName\", \"columnAttrs\", \"column\", \"className\"]);\n\n    let classNameOutput = className;\n\n    if (typeof className !== 'string') {\n      this.logDeprecated('The property \"className\" requires a string'); // This is a deprecated default and will be removed soon.\n\n      if (typeof className === 'undefined') {\n        classNameOutput = 'my-masonry-grid';\n      }\n    }\n\n    return /*#__PURE__*/React.createElement(\"div\", _extends({}, rest, {\n      className: classNameOutput\n    }), this.renderColumns());\n  }\n\n}\n\nMasonry.defaultProps = defaultProps;\n\nexport default Masonry;\n"], "mappings": ";;;;;;;;AAAA,mBAAkB;AAElB,SAAS,yBAAyB,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAAS,8BAA8B,QAAQ,QAAQ;AAAG,MAAI,KAAK;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAAG,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAAE,YAAM,iBAAiB,CAAC;AAAG,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAAU,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAE3e,SAAS,8BAA8B,QAAQ,UAAU;AAAE,MAAI,UAAU,KAAM,QAAO,CAAC;AAAG,MAAI,SAAS,CAAC;AAAG,MAAI,aAAa,OAAO,KAAK,MAAM;AAAG,MAAI,KAAK;AAAG,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAAE,UAAM,WAAW,CAAC;AAAG,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAAU,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAAG;AAAE,SAAO;AAAQ;AAElT,SAAS,WAAW;AAAE,aAAW,OAAO,UAAU,SAAU,QAAQ;AAAE,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,UAAI,SAAS,UAAU,CAAC;AAAG,eAAS,OAAO,QAAQ;AAAE,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAAG;AAAA,MAAE;AAAA,IAAE;AAAE,WAAO;AAAA,EAAQ;AAAG,SAAO,SAAS,MAAM,MAAM,SAAS;AAAG;AAE5T,SAAS,QAAQ,QAAQ,gBAAgB;AAAE,MAAI,OAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,QAAI,eAAgB,WAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IAAY,CAAC;AAAG,SAAK,KAAK,MAAM,MAAM,OAAO;AAAA,EAAG;AAAE,SAAO;AAAM;AAEpV,SAAS,cAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,GAAG;AAAE,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AAAE,wBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG,WAAW,OAAO,2BAA2B;AAAE,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAAG,OAAO;AAAE,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAErhB,SAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,MAAI,OAAO,KAAK;AAAE,WAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,EAAG,OAAO;AAAE,QAAI,GAAG,IAAI;AAAA,EAAO;AAAE,SAAO;AAAK;AAChN,IAAM,eAAe;AAAA,EACnB,gBAAgB;AAAA;AAAA,EAEhB,WAAW;AAAA;AAAA,EAEX,iBAAiB;AAAA;AAAA;AAAA,EAGjB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,QAAQ;AACV;AACA,IAAM,kBAAkB;AAExB,IAAM,UAAN,cAAsB,aAAAA,QAAM,UAAU;AAAA,EACpC,YAAY,OAAO;AACjB,UAAM,KAAK;AAEX,SAAK,yBAAyB,KAAK,uBAAuB,KAAK,IAAI;AACnE,SAAK,iCAAiC,KAAK,+BAA+B,KAAK,IAAI;AAEnF,QAAI;AAEJ,QAAI,KAAK,MAAM,kBAAkB,KAAK,MAAM,eAAe,SAAS;AAClE,oBAAc,KAAK,MAAM,eAAe;AAAA,IAC1C,OAAO;AACL,oBAAc,SAAS,KAAK,MAAM,cAAc,KAAK;AAAA,IACvD;AAEA,SAAK,QAAQ;AAAA,MACX;AAAA,IACF;AAAA,EACF;AAAA,EAEA,oBAAoB;AAClB,SAAK,uBAAuB;AAE5B,QAAI,QAAQ;AACV,aAAO,iBAAiB,UAAU,KAAK,8BAA8B;AAAA,IACvE;AAAA,EACF;AAAA,EAEA,qBAAqB;AACnB,SAAK,uBAAuB;AAAA,EAC9B;AAAA,EAEA,uBAAuB;AACrB,QAAI,QAAQ;AACV,aAAO,oBAAoB,UAAU,KAAK,8BAA8B;AAAA,IAC1E;AAAA,EACF;AAAA,EAEA,iCAAiC;AAC/B,QAAI,CAAC,UAAU,CAAC,OAAO,uBAAuB;AAE5C,WAAK,uBAAuB;AAC5B;AAAA,IACF;AAEA,QAAI,OAAO,sBAAsB;AAE/B,aAAO,qBAAqB,KAAK,8BAA8B;AAAA,IACjE;AAEA,SAAK,iCAAiC,OAAO,sBAAsB,MAAM;AACvE,WAAK,uBAAuB;AAAA,IAC9B,CAAC;AAAA,EACH;AAAA,EAEA,yBAAyB;AACvB,UAAM,cAAc,UAAU,OAAO,cAAc;AACnD,QAAI,uBAAuB,KAAK,MAAM;AAEtC,QAAI,OAAO,yBAAyB,UAAU;AAC5C,6BAAuB;AAAA,QACrB,SAAS,SAAS,oBAAoB,KAAK;AAAA,MAC7C;AAAA,IACF;AAEA,QAAI,oBAAoB;AACxB,QAAI,UAAU,qBAAqB,WAAW;AAE9C,aAAS,cAAc,sBAAsB;AAC3C,YAAM,gBAAgB,SAAS,UAAU;AACzC,YAAM,sBAAsB,gBAAgB,KAAK,eAAe;AAEhE,UAAI,uBAAuB,gBAAgB,mBAAmB;AAC5D,4BAAoB;AACpB,kBAAU,qBAAqB,UAAU;AAAA,MAC3C;AAAA,IACF;AAEA,cAAU,KAAK,IAAI,GAAG,SAAS,OAAO,KAAK,CAAC;AAE5C,QAAI,KAAK,MAAM,gBAAgB,SAAS;AACtC,WAAK,SAAS;AAAA,QACZ,aAAa;AAAA,MACf,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,iBAAiB;AACf,UAAM,qBAAqB,KAAK,MAAM;AACtC,UAAM,iBAAiB,IAAI,MAAM,kBAAkB;AAEnD,UAAM,QAAQ,aAAAA,QAAM,SAAS,QAAQ,KAAK,MAAM,QAAQ;AAExD,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAM,cAAc,IAAI;AAExB,UAAI,CAAC,eAAe,WAAW,GAAG;AAChC,uBAAe,WAAW,IAAI,CAAC;AAAA,MACjC;AAEA,qBAAe,WAAW,EAAE,KAAK,MAAM,CAAC,CAAC;AAAA,IAC3C;AAEA,WAAO;AAAA,EACT;AAAA,EAEA,gBAAgB;AACd,UAAM;AAAA,MACJ;AAAA,MACA,cAAc,CAAC;AAAA,MACf;AAAA,IACF,IAAI,KAAK;AACT,UAAM,oBAAoB,KAAK,eAAe;AAC9C,UAAM,cAAc,GAAG,MAAM,kBAAkB,MAAM;AACrD,QAAI,YAAY;AAEhB,QAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,WAAK,cAAc,kDAAkD;AAErE,UAAI,OAAO,cAAc,aAAa;AACpC,oBAAY;AAAA,MACd;AAAA,IACF;AAEA,UAAM,mBAAmB,cAAc,cAAc,cAAc,CAAC,GAAG,MAAM,GAAG,WAAW,GAAG,CAAC,GAAG;AAAA,MAChG,OAAO,cAAc,cAAc,CAAC,GAAG,YAAY,KAAK,GAAG,CAAC,GAAG;AAAA,QAC7D,OAAO;AAAA,MACT,CAAC;AAAA,MACD;AAAA,IACF,CAAC;AAED,WAAO,kBAAkB,IAAI,CAAC,OAAO,MAAM;AACzC,aAAoB,aAAAA,QAAM,cAAc,OAAO,SAAS,CAAC,GAAG,kBAAkB;AAAA,QAC5E,KAAK;AAAA,MACP,CAAC,GAAG,KAAK;AAAA,IACX,CAAC;AAAA,EACH;AAAA,EAEA,cAAc,SAAS;AACrB,YAAQ,MAAM,aAAa,OAAO;AAAA,EACpC;AAAA,EAEA,SAAS;AACP,UAAM,cAAc,KAAK,OACnB;AAAA;AAAA,MAEJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA;AAAA,MAEA;AAAA,IACF,IAAI,aACE,OAAO,yBAAyB,aAAa,CAAC,YAAY,kBAAkB,mBAAmB,eAAe,UAAU,WAAW,CAAC;AAE1I,QAAI,kBAAkB;AAEtB,QAAI,OAAO,cAAc,UAAU;AACjC,WAAK,cAAc,4CAA4C;AAE/D,UAAI,OAAO,cAAc,aAAa;AACpC,0BAAkB;AAAA,MACpB;AAAA,IACF;AAEA,WAAoB,aAAAA,QAAM,cAAc,OAAO,SAAS,CAAC,GAAG,MAAM;AAAA,MAChE,WAAW;AAAA,IACb,CAAC,GAAG,KAAK,cAAc,CAAC;AAAA,EAC1B;AAEF;AAEA,QAAQ,eAAe;AAEvB,IAAO,mCAAQ;", "names": ["React"]}