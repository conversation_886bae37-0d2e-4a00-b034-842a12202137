const path = require('path');
const fs = require('fs').promises;
const { spawn } = require('child_process');
const { ipcMain } = require('electron');
const logger = require('./logger');
const DependencyManager = require('./dependencyManager');
const dependencyManager = new DependencyManager();
const trellisServer = require('./trellisServer');

const PYTHON_EXE = 'python'; // TODO: Unify this config
const PIPELINES_DIR = path.join(__dirname, '../../pipelines');

class PipelineLoader {
  constructor() {
    this.pipelines = {};
    this.runningProcesses = {};
  }

  async registerPipelines() {
    logger.info('Registering pipelines...');
    const pipelineFolders = await fs.readdir(PIPELINES_DIR);
    logger.info('[DEBUG] Found pipeline folders:', pipelineFolders);

    for (const folder of pipelineFolders) {
        const configPath = path.join(PIPELINES_DIR, folder, 'config.json');
        logger.info(`[DEBUG] Checking config at: ${configPath}`);

        try {
            const data = await fs.readFile(configPath, 'utf-8');
            if (folder === 'TrellisSource') {
              logger.info(`[DEBUG] TrellisSource config raw type: ${typeof data}`);
              logger.info(`[DEBUG] TrellisSource config raw (first 200 chars): ${data.slice(0, 200)}`);
            }
            this.pipelines[folder] = JSON.parse(data);
            if (folder === 'TrellisSource') {
              logger.info('[DEBUG] TrellisSource config parsed keys:', Object.keys(this.pipelines[folder]));
              logger.info('[DEBUG] TrellisSource config parsed:', this.pipelines[folder]);
            }
            logger.info(`Registered pipeline: ${folder}`);
        } catch (error) {
            logger.warn(`Could not register pipeline ${folder}: ${error.message}`);
            if (folder === 'TrellisSource') {
              logger.error('[DEBUG] TrellisSource registration failed:', error);
            }
        }
    }

    logger.info('[DEBUG] Final registered pipelines:', Object.keys(this.pipelines));
  }

  getRegisteredPipelines() {
    const pipelines = Object.entries(this.pipelines).map(([id, cfg]) => ({
      id,
      name: cfg.name || id,
      description: cfg.description || '',
      available: true, // could be enhanced later
      features: cfg.features || [],
      models: cfg.models || [],
    }));

    logger.info('[DEBUG] getRegisteredPipelines returning:', pipelines.map(p => p.id));
    return pipelines;
  }

  async runPipeline(pipelineName, data = {}, progressCb = () => {}) {
    logger.info(`runPipeline request: ${pipelineName}`);
    logger.info('runPipeline: Registered pipelines at call time:', Object.keys(this.pipelines));

    const pipeline = this.pipelines[pipelineName];
    if (!pipeline) {
      logger.error(`Unregistered pipeline: ${pipelineName}`);
      return { success: false, error: 'Pipeline not found' };
    }

    // --- Start: Dependency Check ---
    let depsOk = await dependencyManager.checkDependencies(pipelineName);
    if (!depsOk) {
      logger.warn(`Dependencies missing for ${pipelineName}. Triggering auto-install…`);
      try {
        await dependencyManager.installDependencies(pipelineName, 'python');
        depsOk = await dependencyManager.checkDependencies(pipelineName);
        if (!depsOk) {
          throw new Error('Dependencies were not satisfied even after a seemingly successful installation.');
        }
        logger.info(`Dependencies installed for ${pipelineName}, continuing…`);
      } catch (error) {
        logger.error(`Dependency installation process failed for ${pipelineName}: ${error.message}`);
        return { success: false, error: 'Dependency installation failed. Please check the logs.' };
      }
    }
    // --- End: Dependency Check ---

    // --- Trellis Integration ---
    if (pipelineName.toLowerCase() === 'trellissource') {
      try {
        // Create a wrapper progress callback that adds required fields for frontend
        let progressCallbackCounter = 0;
        const wrappedProgressCb = (status) => {
          const formattedStatus = {
            event: 'progress',
            session_id: data.input_image_id || 'unknown',
            stage: status.stage || 'trellis',
            progress: status.progress || 0,
            message: status.message || status.description || 'Processing...',
            description: status.message || status.description || 'Processing...'
          };

          progressCallbackCounter++;
          // Only log every 5th progress callback to reduce spam
          if (progressCallbackCounter % 5 === 0 || status.progress >= 100) {
            console.log('[Pipeline Loader] Progress update:', `${status.stage} - ${status.progress}% - ${status.message}`);
          }

          progressCb(formattedStatus);
        };

        // Check if this is text-to-3D generation (has text_prompt but no image path)
        const hasTextPrompt = data.text_prompt && data.text_prompt.trim();
        const hasImagePath = data.image_path || data.input_image_path || data.input_image || data.image;

        // Declare imageOutputPath in broader scope for later use
        let imageOutputPath = null;

        if (hasTextPrompt && !hasImagePath) {
          logger.info('runPipeline: Text-to-3D generation detected, generating image first');

          // Step 1: Generate image from text prompt
          wrappedProgressCb({ stage: 'image_generation', progress: 0, message: 'Generating image from text prompt...' });

          const PipelineManager = require('./pipelineManager');
          const pipelineManager = new PipelineManager(dependencyManager);
          const fs = require('fs').promises;

          // Create output directory for generated image
          const outputDir = path.join(__dirname, '../../output');
          await fs.mkdir(outputDir, { recursive: true });

          const timestamp = Date.now();
          imageOutputPath = path.join(outputDir, `text_generated_${timestamp}.png`);

          try {
            // Map frontend model names to backend model names
            const modelNameMap = {
              // SDXL Turbo variants
              'sdxl_turbo': 'sdxl-turbo',
              'sdxl-turbo': 'sdxl-turbo',
              // SDXL Base variants
              'sdxl': 'stable-diffusion-xl-base-1.0',
              'sdxl_base': 'stable-diffusion-xl-base-1.0',
              'sdxl-base': 'stable-diffusion-xl-base-1.0',
              'stable-diffusion-xl-base-1.0': 'stable-diffusion-xl-base-1.0',
              // SDXL Refiner variants
              'sdxl_refiner': 'stable-diffusion-xl-refiner-1.0',
              'sdxl-refiner': 'stable-diffusion-xl-refiner-1.0',
              'stable-diffusion-xl-refiner-1.0': 'stable-diffusion-xl-refiner-1.0',
              // Stable Diffusion v1.5 variants removed from text-to-3D (incomplete model)
              // 'stable_diffusion': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'sd_v1_5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'sd-v1-5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // 'stable-diffusion-v1-5': 'stable-diffusion-v1-5', // Removed - incomplete model
              // Stable Diffusion v2.1 variants
              'sd_v2_1': 'stable-diffusion-2-1',
              'sd-v2-1': 'stable-diffusion-2-1',
              'stable-diffusion-2-1': 'stable-diffusion-2-1',
              // FLUX variants
              'flux': 'flux-dev',
              'flux_dev': 'flux-dev',
              'flux-dev': 'flux-dev',
              'flux_dev_quantized': 'flux-dev-4bit',
              'flux-dev-quantized': 'flux-dev-4bit',
              'flux_dev_4bit': 'flux-dev-4bit',
              'flux-dev-4bit': 'flux-dev-4bit'
            };

            const frontendModelName = data.selected_model || 'sdxl_turbo';
            const modelName = modelNameMap[frontendModelName] || frontendModelName;

            logger.info(`Text-to-3D: Frontend model name: ${frontendModelName}, Backend model name: ${modelName}`);

            // Enhance prompt for optimal 3D generation
            const enhancedPrompt = `${data.text_prompt}, isolated object on white background, centered, full view, clean white studio lighting, professional product photography, all parts visible, complete object, no cropping, 3D model reference`;

            const imageResult = await pipelineManager.generateImage(
              enhancedPrompt,
              imageOutputPath,
              {
                model: modelName,
                num_inference_steps: 20,
                guidance_scale: 7.5,
                width: 1024,
                height: 1024
              },
              (progress) => {
                wrappedProgressCb({
                  stage: 'image_generation',
                  progress: Math.round(progress * 25), // Use first 25% for image generation
                  message: `Generating image: ${Math.round(progress * 100)}%`
                });
              }
            );

            if (!imageResult.success) {
              throw new Error(`Image generation failed: ${imageResult.error}`);
            }

            wrappedProgressCb({ stage: 'image_generation', progress: 25, message: 'Image generated successfully, starting 3D generation...' });

            // Step 2: Use generated image for 3D generation
            data.image_path = imageOutputPath;
            data.input_image_path = imageOutputPath;

          } catch (error) {
            logger.error('Image generation failed:', error);
            throw new Error(`Text-to-3D failed during image generation: ${error.message}`);
          }
        }

        wrappedProgressCb({ stage: 'trellis', progress: 25, message: 'Launching Trellis server and generating 3D model...' });
        const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!imagePath) {
          throw new Error('No image path provided for Trellis 3D generation.');
        }
        const modelPath = await trellisServer.generate3DModel(imagePath, wrappedProgressCb);

        // Return both model path and generated image path for text-to-3D workflows
        const result = { success: true, model_path: modelPath };
        if (hasTextPrompt && !hasImagePath) {
          // For text-to-3D generation, include the generated image path for thumbnail creation
          result.generated_image_path = imageOutputPath;
        }
        return result;
      } catch (err) {
        logger.error('Trellis 3D generation failed:', err);
        return { success: false, error: err.message };
      }
    }
    // --- End Trellis Integration ---

    // --- Hunyaun3d-2 Integration ---
    if (pipelineName.toLowerCase() === 'hunyaun3d-2') {
      try {
        const hunyaunServer = require('./hunyaunServer');

        // Create a wrapper progress callback that adds required fields for frontend
        let progressCallbackCounter = 0;
        const wrappedProgressCb = (status) => {
          const formattedStatus = {
            event: 'progress',
            session_id: data.input_image_id || 'unknown',
            stage: status.stage || 'hunyaun',
            progress: status.progress || 0,
            message: status.message || status.description || 'Processing...',
            timestamp: new Date().toISOString()
          };

          progressCallbackCounter++;
          if (progressCallbackCounter % 5 === 0 || status.progress >= 100) {
            logger.info(`[Hunyaun Progress] ${formattedStatus.stage}: ${formattedStatus.progress}% - ${formattedStatus.message}`);
          }

          if (progressCb) {
            progressCb(formattedStatus);
          }
        };

        // Handle text-to-3D generation (text prompt without image)
        const hasTextPrompt = data.text_prompt && data.text_prompt.trim();
        const hasImagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        let imageOutputPath = null;

        if (hasTextPrompt && !hasImagePath) {
          wrappedProgressCb({ stage: 'image_generation', progress: 0, message: 'Generating image from text prompt...' });

          try {
            // Generate image first using the image generation pipeline
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            imageOutputPath = path.join(OUTPUT_DIR, `generated_image_${timestamp}.png`);

            const imageResult = await this.runPipeline(
              'ImageGeneration',
              {
                prompt: data.text_prompt,
                output_path: imageOutputPath,
                model: data.selected_model || 'sdxl-turbo',
                settings: data.settings || {}
              },
              (imageProgress) => {
                wrappedProgressCb({
                  stage: 'image_generation',
                  progress: Math.min(imageProgress.progress || 0, 24),
                  message: imageProgress.message || 'Generating image...'
                });
              }
            );

            if (!imageResult.success) {
              throw new Error(`Image generation failed: ${imageResult.error}`);
            }

            wrappedProgressCb({ stage: 'image_generation', progress: 25, message: 'Image generated successfully, starting 3D generation...' });

            // Step 2: Use generated image for 3D generation
            data.image_path = imageOutputPath;
            data.input_image_path = imageOutputPath;

          } catch (error) {
            logger.error('Image generation failed:', error);
            throw new Error(`Text-to-3D failed during image generation: ${error.message}`);
          }
        }

        wrappedProgressCb({ stage: 'hunyaun', progress: 25, message: 'Launching Hunyaun server and generating 3D model...' });
        const imagePath = data.image_path || data.input_image_path || data.input_image || data.image;
        if (!imagePath) {
          throw new Error('No image path provided for Hunyaun 3D generation.');
        }
        const modelPath = await hunyaunServer.generate3DModel(imagePath, wrappedProgressCb, data.settings || {});

        // Return both model path and generated image path for text-to-3D workflows
        const result = { success: true, model_path: modelPath };
        if (hasTextPrompt && !hasImagePath) {
          // For text-to-3D generation, include the generated image path for thumbnail creation
          result.generated_image_path = imageOutputPath;
        }
        return result;
      } catch (err) {
        logger.error('Hunyaun 3D generation failed:', err);
        return { success: false, error: err.message };
      }
    }
    // --- End Hunyaun3d-2 Integration ---

    const pythonExeForPipeline = dependencyManager._getPythonExe(pipelineName);

    // Helper to run python script and capture stdout
    const runScript = (scriptPath, scriptArgs = []) => {
      return new Promise((resolve, reject) => {
        logger.info(`Spawning: ${pythonExeForPipeline} ${[scriptPath, ...scriptArgs].join(' ')}`);

        const env = { ...process.env };
        const scriptsDir = path.dirname(pythonExeForPipeline);
        delete env.PYTHONHOME;
        env.VIRTUAL_ENV = path.join(PIPELINES_DIR, pipelineName, 'venv');
        env.PATH = `${scriptsDir}${path.delimiter}${env.PATH}`;

        const proc = spawn(pythonExeForPipeline, [scriptPath, ...scriptArgs], {
          env,
          windowsHide: true
        });

        let stdout = '';
        let stderr = '';
        let leftover = '';

        proc.stdout.on('data', (d) => {
          const txtChunk = d.toString();
          stdout += txtChunk;

          const combined = leftover + txtChunk;
          const lines = combined.split(/\r?\n/);
          leftover = lines.pop() || '';

          for (const line of lines) {
            if (!line.trim()) continue;
            try {
              const obj = JSON.parse(line);
              // Forward any structured progress objects directly
              if (obj.event === 'progress') {
                progressCb(obj);
                continue;
              }
            } catch {/* not JSON */}
            // Fallback – send raw text
            progressCb(line);
          }
        });
        proc.stderr.on('data', (d) => {
          stderr += d.toString();
          logger.error(`[${path.basename(scriptPath)}] ${d}`);
        });
        proc.on('close', (code) => {
          if (leftover.trim()) stdout += leftover;
          if (code === 0) resolve(stdout);
          else {
            logger.error(`Script failed with code ${code}. Stderr:\n${stderr}`);
            reject(new Error(`Script exited ${code}: ${stderr.split('\n').slice(-3).join(' ')}`));
          }
        });
      });
    };

    // Stage 1: primary pipeline
    logger.info('runPipeline: pipeline config:', JSON.stringify(pipeline, null, 2));
    logger.info('runPipeline: pipeline.entry_script:', pipeline.entry_script);
    if (!pipeline.entry_script) {
      logger.error(`Pipeline ${pipelineName} is missing entry_script in config!`);
      return { success: false, error: 'Pipeline entry_script missing in config.' };
    }
    const scriptPath = path.join(PIPELINES_DIR, pipelineName, pipeline.entry_script);

    // Pass data as JSON string arg
    const resultJsonRaw = await runScript(scriptPath, [JSON.stringify(data)]);
    // Parse the last non-empty line as JSON to be robust against additional prints
    const resultLines = resultJsonRaw.trim().split(/\r?\n/).filter(l => l.trim().length > 0);
    const resultJson = resultLines[resultLines.length - 1];
    let result;
    try {
      result = JSON.parse(resultJson);
    } catch (err) {
      logger.error(`Failed to parse pipeline output as JSON. Raw output:\n${resultJsonRaw}`);
      throw new Error('Pipeline returned invalid JSON');
    }

    if (result.status !== 'success') {
      throw new Error(`Pipeline script for ${pipelineName} failed: ${result.message || 'Unknown error'}`);
    }

    let modelPath = result.model_path || null;
    const videoPath = result.video_path || null;

      // If lighting optimizer requested and this is Microsoft TRELLIS
  if ((pipelineName.toLowerCase() === 'microsoft_trellis' || pipelineName.toLowerCase() === 'trellissource') && data.settings?.enable_lighting_optimizer) {
      progressCb({ stage: 'lighting', message: 'Enhancing lighting with Hunyaun3D-2...' });

      const hunPipeline = this.pipelines['Hunyaun3d-2'];
      if (!hunPipeline) {
        logger.warn('Hunyaun3D-2 pipeline missing, skipping lighting optimization');
      } else if (modelPath) {
        // This part needs re-evaluation as it references a legacy script
        // For now, we'll log a warning and skip.
        logger.warn('Lighting enhancement step is defined but uses a legacy path. Skipping.');
        // const lightingScript = path.join(_legacyDir, 'pipelines', 'trellis_pipeline', 'lighting_enhancement.py');
        // try {
        //   await runScript(lightingScript, [modelPath]);
        // } catch (e) {
        //   logger.error('Lighting enhancement failed:', e);
        // }
      }
    }

    return { success: true, model_path: modelPath, video_path: videoPath };
  }
  
  initializeIPC() {
    ipcMain.on('run-pipeline', (event, { name, args }) => {
        this.runPipeline(name, args);
    });
  }
}

const loader = new PipelineLoader();

(async () => {
  await loader.registerPipelines();

  // Merge pipeline loader configs with dependency manager's embedded configs
  // Priority: embedded configs override file configs for consistency
  for (const [pipelineName, config] of Object.entries(loader.pipelines)) {
    if (!dependencyManager.pipelines[pipelineName]) {
      // Only add if not already present in embedded configs
      dependencyManager.pipelines[pipelineName] = config;
    } else {
      logger.info(`Pipeline ${pipelineName} already exists in embedded configs, keeping embedded version`);
    }
  }

  dependencyManager.dependencyStatus = dependencyManager.dependencyStatus || {};
  for (const pipelineName of Object.keys(dependencyManager.pipelines)) {
    if (!dependencyManager.dependencyStatus[pipelineName]) {
      dependencyManager.dependencyStatus[pipelineName] = {
        name: pipelineName,
        python: { installed: false, details: {} },
        models: { installed: false, details: {} }
      };
    }
  }
  logger.info('PipelineLoader: Final pipelines after merge:', Object.keys(dependencyManager.pipelines));
  loader.initializeIPC();
})();

module.exports = loader; 